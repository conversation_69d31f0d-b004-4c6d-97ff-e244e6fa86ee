CMakeFiles/mini_tool.dir/src/MainFrame.cpp.o: \
 /home/<USER>/lsq/mini_tool/src/MainFrame.cpp /usr/include/stdc-predef.h \
 /home/<USER>/lsq/mini_tool/src/MainFrame.h /usr/include/wx-3.0/wx/wx.h \
 /usr/include/wx-3.0/wx/defs.h /usr/include/wx-3.0/wx/platform.h \
 /usr/include/wx-3.0/wx/compiler.h \
 /usr/lib/x86_64-linux-gnu/wx/include/gtk3-unicode-3.0/wx/setup.h \
 /usr/include/wx-3.0/wx/chkconf.h /usr/include/wx-3.0/wx/gtk/chkconf.h \
 /usr/include/wx-3.0/wx/unix/chkconf.h /usr/include/wx-3.0/wx/version.h \
 /usr/include/wx-3.0/wx/cpp.h /usr/include/wx-3.0/wx/dlimpexp.h \
 /usr/lib/gcc/x86_64-linux-gnu/9/include/stddef.h \
 /usr/include/wx-3.0/wx/debug.h /usr/include/assert.h \
 /usr/include/features.h /usr/include/x86_64-linux-gnu/sys/cdefs.h \
 /usr/include/x86_64-linux-gnu/bits/wordsize.h \
 /usr/include/x86_64-linux-gnu/bits/long-double.h \
 /usr/include/x86_64-linux-gnu/gnu/stubs.h \
 /usr/include/x86_64-linux-gnu/gnu/stubs-64.h \
 /usr/lib/gcc/x86_64-linux-gnu/9/include/limits.h \
 /usr/lib/gcc/x86_64-linux-gnu/9/include/syslimits.h \
 /usr/include/limits.h \
 /usr/include/x86_64-linux-gnu/bits/libc-header-start.h \
 /usr/include/x86_64-linux-gnu/bits/posix1_lim.h \
 /usr/include/x86_64-linux-gnu/bits/local_lim.h \
 /usr/include/linux/limits.h \
 /usr/include/x86_64-linux-gnu/bits/posix2_lim.h \
 /usr/include/x86_64-linux-gnu/bits/xopen_lim.h \
 /usr/include/x86_64-linux-gnu/bits/uio_lim.h \
 /usr/include/wx-3.0/wx/chartype.h /usr/include/wchar.h \
 /usr/include/x86_64-linux-gnu/bits/floatn.h \
 /usr/include/x86_64-linux-gnu/bits/floatn-common.h \
 /usr/lib/gcc/x86_64-linux-gnu/9/include/stdarg.h \
 /usr/include/x86_64-linux-gnu/bits/wchar.h \
 /usr/include/x86_64-linux-gnu/bits/types/wint_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/mbstate_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/__mbstate_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/__FILE.h \
 /usr/include/x86_64-linux-gnu/bits/types/FILE.h \
 /usr/include/x86_64-linux-gnu/bits/types/locale_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/__locale_t.h \
 /usr/include/x86_64-linux-gnu/sys/types.h \
 /usr/include/x86_64-linux-gnu/bits/types.h \
 /usr/include/x86_64-linux-gnu/bits/timesize.h \
 /usr/include/x86_64-linux-gnu/bits/typesizes.h \
 /usr/include/x86_64-linux-gnu/bits/time64.h \
 /usr/include/x86_64-linux-gnu/bits/types/clock_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/clockid_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/time_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/timer_t.h \
 /usr/include/x86_64-linux-gnu/bits/stdint-intn.h /usr/include/endian.h \
 /usr/include/x86_64-linux-gnu/bits/endian.h \
 /usr/include/x86_64-linux-gnu/bits/endianness.h \
 /usr/include/x86_64-linux-gnu/bits/byteswap.h \
 /usr/include/x86_64-linux-gnu/bits/uintn-identity.h \
 /usr/include/x86_64-linux-gnu/sys/select.h \
 /usr/include/x86_64-linux-gnu/bits/select.h \
 /usr/include/x86_64-linux-gnu/bits/types/sigset_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/__sigset_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/struct_timeval.h \
 /usr/include/x86_64-linux-gnu/bits/types/struct_timespec.h \
 /usr/include/x86_64-linux-gnu/bits/pthreadtypes.h \
 /usr/include/x86_64-linux-gnu/bits/thread-shared-types.h \
 /usr/include/x86_64-linux-gnu/bits/pthreadtypes-arch.h \
 /usr/include/x86_64-linux-gnu/bits/struct_mutex.h \
 /usr/include/x86_64-linux-gnu/bits/struct_rwlock.h \
 /usr/include/wx-3.0/wx/windowid.h /usr/include/wx-3.0/wx/features.h \
 /usr/include/wx-3.0/wx/object.h /usr/include/wx-3.0/wx/memory.h \
 /usr/include/wx-3.0/wx/string.h /usr/include/string.h \
 /usr/include/strings.h /usr/include/stdio.h \
 /usr/include/x86_64-linux-gnu/bits/types/__fpos_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/__fpos64_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/struct_FILE.h \
 /usr/include/x86_64-linux-gnu/bits/types/cookie_io_functions_t.h \
 /usr/include/x86_64-linux-gnu/bits/stdio_lim.h \
 /usr/include/x86_64-linux-gnu/bits/sys_errlist.h \
 /usr/include/c++/9/stdlib.h /usr/include/c++/9/cstdlib \
 /usr/include/x86_64-linux-gnu/c++/9/bits/c++config.h \
 /usr/include/x86_64-linux-gnu/c++/9/bits/os_defines.h \
 /usr/include/x86_64-linux-gnu/c++/9/bits/cpu_defines.h \
 /usr/include/c++/9/pstl/pstl_config.h /usr/include/stdlib.h \
 /usr/include/x86_64-linux-gnu/bits/waitflags.h \
 /usr/include/x86_64-linux-gnu/bits/waitstatus.h /usr/include/alloca.h \
 /usr/include/x86_64-linux-gnu/bits/stdlib-float.h \
 /usr/include/c++/9/bits/std_abs.h /usr/include/wx-3.0/wx/wxcrtbase.h \
 /usr/include/ctype.h /usr/include/wctype.h \
 /usr/include/x86_64-linux-gnu/bits/wctype-wchar.h /usr/include/time.h \
 /usr/include/x86_64-linux-gnu/bits/time.h \
 /usr/include/x86_64-linux-gnu/bits/timex.h \
 /usr/include/x86_64-linux-gnu/bits/types/struct_tm.h \
 /usr/include/x86_64-linux-gnu/bits/types/struct_itimerspec.h \
 /usr/include/wx-3.0/wx/strvararg.h /usr/include/wx-3.0/wx/strconv.h \
 /usr/include/wx-3.0/wx/buffer.h /usr/include/wx-3.0/wx/fontenc.h \
 /usr/include/wx-3.0/wx/unichar.h /usr/include/wx-3.0/wx/stringimpl.h \
 /usr/include/wx-3.0/wx/beforestd.h /usr/include/c++/9/string \
 /usr/include/c++/9/bits/stringfwd.h /usr/include/c++/9/bits/memoryfwd.h \
 /usr/include/c++/9/bits/char_traits.h \
 /usr/include/c++/9/bits/stl_algobase.h \
 /usr/include/c++/9/bits/functexcept.h \
 /usr/include/c++/9/bits/exception_defines.h \
 /usr/include/c++/9/bits/cpp_type_traits.h \
 /usr/include/c++/9/ext/type_traits.h \
 /usr/include/c++/9/ext/numeric_traits.h \
 /usr/include/c++/9/bits/stl_pair.h /usr/include/c++/9/bits/move.h \
 /usr/include/c++/9/bits/concept_check.h /usr/include/c++/9/type_traits \
 /usr/include/c++/9/bits/stl_iterator_base_types.h \
 /usr/include/c++/9/bits/stl_iterator_base_funcs.h \
 /usr/include/c++/9/debug/assertions.h \
 /usr/include/c++/9/bits/stl_iterator.h \
 /usr/include/c++/9/bits/ptr_traits.h /usr/include/c++/9/debug/debug.h \
 /usr/include/c++/9/bits/predefined_ops.h \
 /usr/include/c++/9/bits/postypes.h /usr/include/c++/9/cwchar \
 /usr/include/c++/9/cstdint \
 /usr/lib/gcc/x86_64-linux-gnu/9/include/stdint.h /usr/include/stdint.h \
 /usr/include/x86_64-linux-gnu/bits/stdint-uintn.h \
 /usr/include/c++/9/bits/allocator.h \
 /usr/include/x86_64-linux-gnu/c++/9/bits/c++allocator.h \
 /usr/include/c++/9/ext/new_allocator.h /usr/include/c++/9/new \
 /usr/include/c++/9/exception /usr/include/c++/9/bits/exception.h \
 /usr/include/c++/9/bits/exception_ptr.h \
 /usr/include/c++/9/bits/cxxabi_init_exception.h \
 /usr/include/c++/9/typeinfo /usr/include/c++/9/bits/hash_bytes.h \
 /usr/include/c++/9/bits/nested_exception.h \
 /usr/include/c++/9/bits/localefwd.h \
 /usr/include/x86_64-linux-gnu/c++/9/bits/c++locale.h \
 /usr/include/c++/9/clocale /usr/include/locale.h \
 /usr/include/x86_64-linux-gnu/bits/locale.h /usr/include/c++/9/iosfwd \
 /usr/include/c++/9/cctype /usr/include/c++/9/bits/ostream_insert.h \
 /usr/include/c++/9/bits/cxxabi_forced.h \
 /usr/include/c++/9/bits/stl_function.h \
 /usr/include/c++/9/backward/binders.h \
 /usr/include/c++/9/bits/range_access.h \
 /usr/include/c++/9/initializer_list \
 /usr/include/c++/9/bits/basic_string.h \
 /usr/include/c++/9/ext/atomicity.h \
 /usr/include/x86_64-linux-gnu/c++/9/bits/gthr.h \
 /usr/include/x86_64-linux-gnu/c++/9/bits/gthr-default.h \
 /usr/include/pthread.h /usr/include/sched.h \
 /usr/include/x86_64-linux-gnu/bits/sched.h \
 /usr/include/x86_64-linux-gnu/bits/types/struct_sched_param.h \
 /usr/include/x86_64-linux-gnu/bits/cpu-set.h \
 /usr/include/x86_64-linux-gnu/bits/setjmp.h \
 /usr/include/x86_64-linux-gnu/c++/9/bits/atomic_word.h \
 /usr/include/c++/9/ext/alloc_traits.h \
 /usr/include/c++/9/bits/alloc_traits.h /usr/include/c++/9/string_view \
 /usr/include/c++/9/limits /usr/include/c++/9/bits/functional_hash.h \
 /usr/include/c++/9/bits/string_view.tcc \
 /usr/include/c++/9/ext/string_conversions.h /usr/include/c++/9/cstdio \
 /usr/include/c++/9/cerrno /usr/include/errno.h \
 /usr/include/x86_64-linux-gnu/bits/errno.h /usr/include/linux/errno.h \
 /usr/include/x86_64-linux-gnu/asm/errno.h \
 /usr/include/asm-generic/errno.h /usr/include/asm-generic/errno-base.h \
 /usr/include/x86_64-linux-gnu/bits/types/error_t.h \
 /usr/include/c++/9/bits/basic_string.tcc \
 /usr/include/wx-3.0/wx/afterstd.h /usr/include/wx-3.0/wx/stringops.h \
 /usr/include/wx-3.0/wx/iosfwrap.h /usr/include/wx-3.0/wx/crt.h \
 /usr/include/wx-3.0/wx/wxcrt.h /usr/include/wx-3.0/wx/wxcrtvararg.h \
 /usr/include/wx-3.0/wx/msgout.h /usr/include/wx-3.0/wx/xti.h \
 /usr/include/wx-3.0/wx/rtti.h /usr/include/wx-3.0/wx/flags.h \
 /usr/include/wx-3.0/wx/xti2.h /usr/include/wx-3.0/wx/dynarray.h \
 /usr/include/wx-3.0/wx/list.h /usr/include/wx-3.0/wx/vector.h \
 /usr/include/wx-3.0/wx/scopeguard.h /usr/include/wx-3.0/wx/except.h \
 /usr/include/wx-3.0/wx/meta/movable.h /usr/include/wx-3.0/wx/meta/pod.h \
 /usr/include/wx-3.0/wx/meta/if.h /usr/include/wx-3.0/wx/hash.h \
 /usr/include/wx-3.0/wx/hashmap.h /usr/include/wx-3.0/wx/arrstr.h \
 /usr/include/c++/9/iterator /usr/include/c++/9/ostream \
 /usr/include/c++/9/ios /usr/include/c++/9/bits/ios_base.h \
 /usr/include/c++/9/bits/locale_classes.h \
 /usr/include/c++/9/bits/locale_classes.tcc \
 /usr/include/c++/9/system_error \
 /usr/include/x86_64-linux-gnu/c++/9/bits/error_constants.h \
 /usr/include/c++/9/stdexcept /usr/include/c++/9/streambuf \
 /usr/include/c++/9/bits/streambuf.tcc \
 /usr/include/c++/9/bits/basic_ios.h \
 /usr/include/c++/9/bits/locale_facets.h /usr/include/c++/9/cwctype \
 /usr/include/x86_64-linux-gnu/c++/9/bits/ctype_base.h \
 /usr/include/c++/9/bits/streambuf_iterator.h \
 /usr/include/x86_64-linux-gnu/c++/9/bits/ctype_inline.h \
 /usr/include/c++/9/bits/locale_facets.tcc \
 /usr/include/c++/9/bits/basic_ios.tcc \
 /usr/include/c++/9/bits/ostream.tcc /usr/include/c++/9/istream \
 /usr/include/c++/9/bits/istream.tcc \
 /usr/include/c++/9/bits/stream_iterator.h /usr/include/wx-3.0/wx/intl.h \
 /usr/include/wx-3.0/wx/translation.h /usr/include/wx-3.0/wx/language.h \
 /usr/include/wx-3.0/wx/scopedptr.h \
 /usr/include/wx-3.0/wx/checkeddelete.h /usr/include/wx-3.0/wx/log.h \
 /usr/include/wx-3.0/wx/thread.h /usr/include/wx-3.0/wx/generic/logg.h \
 /usr/include/wx-3.0/wx/event.h /usr/include/wx-3.0/wx/clntdata.h \
 /usr/include/wx-3.0/wx/gdicmn.h /usr/include/wx-3.0/wx/math.h \
 /usr/include/c++/9/math.h /usr/include/c++/9/cmath /usr/include/math.h \
 /usr/include/x86_64-linux-gnu/bits/math-vector.h \
 /usr/include/x86_64-linux-gnu/bits/libm-simd-decl-stubs.h \
 /usr/include/x86_64-linux-gnu/bits/flt-eval-method.h \
 /usr/include/x86_64-linux-gnu/bits/fp-logb.h \
 /usr/include/x86_64-linux-gnu/bits/fp-fast.h \
 /usr/include/x86_64-linux-gnu/bits/mathcalls-helper-functions.h \
 /usr/include/x86_64-linux-gnu/bits/mathcalls.h \
 /usr/include/x86_64-linux-gnu/bits/mathcalls-narrow.h \
 /usr/include/x86_64-linux-gnu/bits/iscanonical.h \
 /usr/include/c++/9/bits/specfun.h /usr/include/c++/9/tr1/gamma.tcc \
 /usr/include/c++/9/tr1/special_function_util.h \
 /usr/include/c++/9/tr1/bessel_function.tcc \
 /usr/include/c++/9/tr1/beta_function.tcc \
 /usr/include/c++/9/tr1/ell_integral.tcc \
 /usr/include/c++/9/tr1/exp_integral.tcc \
 /usr/include/c++/9/tr1/hypergeometric.tcc \
 /usr/include/c++/9/tr1/legendre_function.tcc \
 /usr/include/c++/9/tr1/modified_bessel_func.tcc \
 /usr/include/c++/9/tr1/poly_hermite.tcc \
 /usr/include/c++/9/tr1/poly_laguerre.tcc \
 /usr/include/c++/9/tr1/riemann_zeta.tcc /usr/include/wx-3.0/wx/cursor.h \
 /usr/include/wx-3.0/wx/gtk/cursor.h /usr/include/wx-3.0/wx/gdiobj.h \
 /usr/include/wx-3.0/wx/utils.h /usr/include/wx-3.0/wx/filefn.h \
 /usr/include/x86_64-linux-gnu/sys/stat.h \
 /usr/include/x86_64-linux-gnu/bits/stat.h \
 /usr/include/x86_64-linux-gnu/bits/statx.h /usr/include/linux/stat.h \
 /usr/include/linux/types.h /usr/include/x86_64-linux-gnu/asm/types.h \
 /usr/include/asm-generic/types.h /usr/include/asm-generic/int-ll64.h \
 /usr/include/x86_64-linux-gnu/asm/bitsperlong.h \
 /usr/include/asm-generic/bitsperlong.h /usr/include/linux/posix_types.h \
 /usr/include/linux/stddef.h \
 /usr/include/x86_64-linux-gnu/asm/posix_types.h \
 /usr/include/x86_64-linux-gnu/asm/posix_types_64.h \
 /usr/include/asm-generic/posix_types.h \
 /usr/include/x86_64-linux-gnu/bits/statx-generic.h \
 /usr/include/x86_64-linux-gnu/bits/types/struct_statx_timestamp.h \
 /usr/include/x86_64-linux-gnu/bits/types/struct_statx.h \
 /usr/include/unistd.h /usr/include/x86_64-linux-gnu/bits/posix_opt.h \
 /usr/include/x86_64-linux-gnu/bits/environments.h \
 /usr/include/x86_64-linux-gnu/bits/confname.h \
 /usr/include/x86_64-linux-gnu/bits/getopt_posix.h \
 /usr/include/x86_64-linux-gnu/bits/getopt_core.h \
 /usr/include/x86_64-linux-gnu/bits/unistd_ext.h /usr/include/dirent.h \
 /usr/include/x86_64-linux-gnu/bits/dirent.h \
 /usr/include/x86_64-linux-gnu/bits/dirent_ext.h /usr/include/fcntl.h \
 /usr/include/x86_64-linux-gnu/bits/fcntl.h \
 /usr/include/x86_64-linux-gnu/bits/fcntl-linux.h \
 /usr/include/x86_64-linux-gnu/bits/types/struct_iovec.h \
 /usr/include/linux/falloc.h /usr/include/wx-3.0/wx/versioninfo.h \
 /usr/include/wx-3.0/wx/meta/implicitconversion.h \
 /usr/include/wx-3.0/wx/mousestate.h /usr/include/wx-3.0/wx/kbdstate.h \
 /usr/include/wx-3.0/wx/longlong.h /usr/include/wx-3.0/wx/platinfo.h \
 /usr/include/wx-3.0/wx/tracker.h /usr/include/wx-3.0/wx/typeinfo.h \
 /usr/include/wx-3.0/wx/any.h /usr/include/wx-3.0/wx/datetime.h \
 /usr/include/wx-3.0/wx/anystr.h /usr/include/wx-3.0/wx/variant.h \
 /usr/include/wx-3.0/wx/meta/convertible.h \
 /usr/include/wx-3.0/wx/meta/removeref.h /usr/include/wx-3.0/wx/app.h \
 /usr/include/wx-3.0/wx/eventfilter.h /usr/include/wx-3.0/wx/build.h \
 /usr/include/wx-3.0/wx/cmdargs.h /usr/include/wx-3.0/wx/init.h \
 /usr/include/wx-3.0/wx/unix/app.h /usr/include/signal.h \
 /usr/include/x86_64-linux-gnu/bits/signum.h \
 /usr/include/x86_64-linux-gnu/bits/signum-generic.h \
 /usr/include/x86_64-linux-gnu/bits/types/sig_atomic_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/siginfo_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/__sigval_t.h \
 /usr/include/x86_64-linux-gnu/bits/siginfo-arch.h \
 /usr/include/x86_64-linux-gnu/bits/siginfo-consts.h \
 /usr/include/x86_64-linux-gnu/bits/siginfo-consts-arch.h \
 /usr/include/x86_64-linux-gnu/bits/types/sigval_t.h \
 /usr/include/x86_64-linux-gnu/bits/types/sigevent_t.h \
 /usr/include/x86_64-linux-gnu/bits/sigevent-consts.h \
 /usr/include/x86_64-linux-gnu/bits/sigaction.h \
 /usr/include/x86_64-linux-gnu/bits/sigcontext.h \
 /usr/include/x86_64-linux-gnu/bits/types/stack_t.h \
 /usr/include/x86_64-linux-gnu/sys/ucontext.h \
 /usr/include/x86_64-linux-gnu/bits/sigstack.h \
 /usr/include/x86_64-linux-gnu/bits/ss_flags.h \
 /usr/include/x86_64-linux-gnu/bits/types/struct_sigstack.h \
 /usr/include/x86_64-linux-gnu/bits/sigthread.h \
 /usr/include/x86_64-linux-gnu/bits/signal_ext.h \
 /usr/include/wx-3.0/wx/gtk/app.h /usr/include/wx-3.0/wx/stream.h \
 /usr/include/wx-3.0/wx/stopwatch.h /usr/include/wx-3.0/wx/time.h \
 /usr/include/wx-3.0/wx/timer.h /usr/include/wx-3.0/wx/module.h \
 /usr/include/wx-3.0/wx/window.h /usr/include/wx-3.0/wx/font.h \
 /usr/include/wx-3.0/wx/gtk/font.h /usr/include/wx-3.0/wx/colour.h \
 /usr/include/wx-3.0/wx/gtk/colour.h /usr/include/wx-3.0/wx/region.h \
 /usr/include/wx-3.0/wx/gtk/region.h /usr/include/wx-3.0/wx/validate.h \
 /usr/include/wx-3.0/wx/palette.h \
 /usr/include/wx-3.0/wx/generic/paletteg.h /usr/include/wx-3.0/wx/accel.h \
 /usr/include/wx-3.0/wx/gtk/accel.h \
 /usr/include/wx-3.0/wx/generic/accel.h \
 /usr/include/wx-3.0/wx/gtk/window.h /usr/include/wx-3.0/wx/containr.h \
 /usr/include/wx-3.0/wx/panel.h /usr/include/wx-3.0/wx/generic/panelg.h \
 /usr/include/wx-3.0/wx/bitmap.h /usr/include/wx-3.0/wx/image.h \
 /usr/include/wx-3.0/wx/imagbmp.h /usr/include/wx-3.0/wx/imagpng.h \
 /usr/include/wx-3.0/wx/imaggif.h /usr/include/wx-3.0/wx/imagpcx.h \
 /usr/include/wx-3.0/wx/imagjpeg.h /usr/include/wx-3.0/wx/imagtga.h \
 /usr/include/wx-3.0/wx/imagtiff.h /usr/include/wx-3.0/wx/imagpnm.h \
 /usr/include/wx-3.0/wx/imagxpm.h /usr/include/wx-3.0/wx/imagiff.h \
 /usr/include/wx-3.0/wx/gtk/bitmap.h /usr/include/wx-3.0/wx/toplevel.h \
 /usr/include/wx-3.0/wx/nonownedwnd.h \
 /usr/include/wx-3.0/wx/gtk/nonownedwnd.h \
 /usr/include/wx-3.0/wx/iconbndl.h /usr/include/wx-3.0/wx/icon.h \
 /usr/include/wx-3.0/wx/iconloc.h /usr/include/wx-3.0/wx/generic/icon.h \
 /usr/include/wx-3.0/wx/weakref.h /usr/include/wx-3.0/wx/meta/int2type.h \
 /usr/include/wx-3.0/wx/gtk/toplevel.h /usr/include/wx-3.0/wx/frame.h \
 /usr/include/wx-3.0/wx/statusbr.h /usr/include/wx-3.0/wx/control.h \
 /usr/include/wx-3.0/wx/gtk/control.h \
 /usr/include/wx-3.0/wx/generic/statusbr.h /usr/include/wx-3.0/wx/pen.h \
 /usr/include/wx-3.0/wx/gtk/pen.h /usr/include/wx-3.0/wx/gtk/frame.h \
 /usr/include/wx-3.0/wx/dc.h /usr/include/wx-3.0/wx/brush.h \
 /usr/include/wx-3.0/wx/gtk/brush.h \
 /usr/include/wx-3.0/wx/affinematrix2d.h \
 /usr/include/wx-3.0/wx/affinematrix2dbase.h \
 /usr/include/wx-3.0/wx/geometry.h /usr/include/wx-3.0/wx/dcclient.h \
 /usr/include/wx-3.0/wx/dcmemory.h /usr/include/wx-3.0/wx/dcprint.h \
 /usr/include/wx-3.0/wx/dcscreen.h /usr/include/wx-3.0/wx/button.h \
 /usr/include/wx-3.0/wx/anybutton.h \
 /usr/include/wx-3.0/wx/gtk/anybutton.h \
 /usr/include/wx-3.0/wx/gtk/button.h /usr/include/wx-3.0/wx/menuitem.h \
 /usr/include/wx-3.0/wx/gtk/menuitem.h /usr/include/wx-3.0/wx/menu.h \
 /usr/include/wx-3.0/wx/gtk/menu.h /usr/include/wx-3.0/wx/dialog.h \
 /usr/include/wx-3.0/wx/sharedptr.h /usr/include/wx-3.0/wx/atomic.h \
 /usr/include/wx-3.0/wx/gtk/dialog.h /usr/include/wx-3.0/wx/settings.h \
 /usr/include/wx-3.0/wx/msgdlg.h /usr/include/wx-3.0/wx/stockitem.h \
 /usr/include/wx-3.0/wx/generic/msgdlgg.h \
 /usr/include/wx-3.0/wx/gtk/msgdlg.h /usr/include/wx-3.0/wx/dataobj.h \
 /usr/include/wx-3.0/wx/gtk/dataform.h \
 /usr/include/wx-3.0/wx/gtk/dataobj.h \
 /usr/include/wx-3.0/wx/gtk/dataobj2.h /usr/include/wx-3.0/wx/ctrlsub.h \
 /usr/include/wx-3.0/wx/bmpbuttn.h /usr/include/wx-3.0/wx/gtk/bmpbuttn.h \
 /usr/include/wx-3.0/wx/checkbox.h /usr/include/wx-3.0/wx/gtk/checkbox.h \
 /usr/include/wx-3.0/wx/checklst.h /usr/include/wx-3.0/wx/listbox.h \
 /usr/include/wx-3.0/wx/gtk/listbox.h \
 /usr/include/wx-3.0/wx/gtk/checklst.h /usr/include/wx-3.0/wx/choice.h \
 /usr/include/wx-3.0/wx/gtk/choice.h /usr/include/wx-3.0/wx/scrolbar.h \
 /usr/include/wx-3.0/wx/gtk/scrolbar.h /usr/include/wx-3.0/wx/stattext.h \
 /usr/include/wx-3.0/wx/gtk/stattext.h /usr/include/wx-3.0/wx/statbmp.h \
 /usr/include/wx-3.0/wx/gtk/statbmp.h /usr/include/wx-3.0/wx/statbox.h \
 /usr/include/wx-3.0/wx/gtk/statbox.h /usr/include/wx-3.0/wx/radiobox.h \
 /usr/include/wx-3.0/wx/gtk/radiobox.h /usr/include/wx-3.0/wx/radiobut.h \
 /usr/include/wx-3.0/wx/gtk/radiobut.h /usr/include/wx-3.0/wx/textctrl.h \
 /usr/include/wx-3.0/wx/textentry.h \
 /usr/include/wx-3.0/wx/gtk/textentry.h /usr/include/wx-3.0/wx/ioswrap.h \
 /usr/include/c++/9/iostream /usr/include/wx-3.0/wx/gtk/textctrl.h \
 /usr/include/wx-3.0/wx/slider.h /usr/include/wx-3.0/wx/gtk/slider.h \
 /usr/include/wx-3.0/wx/gauge.h /usr/include/wx-3.0/wx/gtk/gauge.h \
 /usr/include/wx-3.0/wx/scrolwin.h /usr/include/wx-3.0/wx/gtk/scrolwin.h \
 /usr/include/wx-3.0/wx/dirdlg.h /usr/include/wx-3.0/wx/gtk/dirdlg.h \
 /usr/include/wx-3.0/wx/toolbar.h /usr/include/wx-3.0/wx/tbarbase.h \
 /usr/include/wx-3.0/wx/gtk/toolbar.h /usr/include/wx-3.0/wx/combobox.h \
 /usr/include/wx-3.0/wx/gtk/combobox.h /usr/include/wx-3.0/wx/layout.h \
 /usr/include/wx-3.0/wx/sizer.h /usr/include/wx-3.0/wx/choicdlg.h \
 /usr/include/wx-3.0/wx/generic/choicdgg.h \
 /usr/include/wx-3.0/wx/textdlg.h \
 /usr/include/wx-3.0/wx/generic/textdlgg.h \
 /usr/include/wx-3.0/wx/valtext.h /usr/include/wx-3.0/wx/filedlg.h \
 /usr/include/wx-3.0/wx/gtk/filedlg.h \
 /usr/include/wx-3.0/wx/gtk/filectrl.h /usr/include/wx-3.0/wx/filectrl.h \
 /usr/include/wx-3.0/wx/mdi.h /usr/include/wx-3.0/wx/gtk/mdi.h \
 /usr/include/c++/9/vector /usr/include/c++/9/bits/stl_construct.h \
 /usr/include/c++/9/bits/stl_uninitialized.h /usr/include/c++/9/utility \
 /usr/include/c++/9/bits/stl_relops.h \
 /usr/include/c++/9/bits/stl_vector.h \
 /usr/include/c++/9/bits/stl_bvector.h /usr/include/c++/9/bits/vector.tcc \
 /home/<USER>/lsq/mini_tool/src/LoginDialog.h \
 /home/<USER>/lsq/mini_tool/src/DeleteSourceDialog.h \
 /home/<USER>/lsq/mini_tool/src/SystemInfoDialog.h \
 /home/<USER>/lsq/mini_tool/src/UdpTestDialog.h \
 /usr/include/wx-3.0/wx/socket.h /usr/include/wx-3.0/wx/sckaddr.h \
 /usr/include/wx-3.0/wx/spinctrl.h /usr/include/wx-3.0/wx/spinbutt.h \
 /usr/include/wx-3.0/wx/range.h /usr/include/wx-3.0/wx/gtk/spinbutt.h \
 /usr/include/wx-3.0/wx/gtk/spinctrl.h \
 /home/<USER>/lsq/mini_tool/src/ChangeConfigDialog.h \
 /home/<USER>/lsq/mini_tool/src/ReplaceAlgorithmDialog.h \
 /usr/include/wx-3.0/wx/filepicker.h /usr/include/wx-3.0/wx/pickerbase.h \
 /usr/include/wx-3.0/wx/filename.h /usr/include/wx-3.0/wx/file.h \
 /usr/include/wx-3.0/wx/convauto.h \
 /usr/include/wx-3.0/wx/gtk/filepicker.h \
 /usr/include/wx-3.0/wx/generic/filepickerg.h \
 /usr/include/wx-3.0/wx/dirctrl.h \
 /usr/include/wx-3.0/wx/generic/dirctrlg.h \
 /usr/include/wx-3.0/wx/treectrl.h /usr/include/wx-3.0/wx/treebase.h \
 /usr/include/wx-3.0/wx/itemid.h \
 /usr/include/wx-3.0/wx/generic/treectlg.h \
 /home/<USER>/lsq/mini_tool/src/AiChatDialog.h \
 /usr/include/wx-3.0/wx/process.h /usr/include/wx-3.0/wx/txtstrm.h \
 /usr/include/wx-3.0/wx/url.h /usr/include/wx-3.0/wx/uri.h \
 /usr/include/wx-3.0/wx/protocol/protocol.h \
 /usr/include/wx-3.0/wx/protocol/http.h \
 /home/<USER>/lsq/mini_tool/src/json.hpp /usr/include/c++/9/algorithm \
 /usr/include/c++/9/bits/stl_algo.h \
 /usr/include/c++/9/bits/algorithmfwd.h \
 /usr/include/c++/9/bits/stl_heap.h /usr/include/c++/9/bits/stl_tempbuf.h \
 /usr/include/c++/9/bits/uniform_int_dist.h \
 /usr/include/c++/9/pstl/glue_algorithm_defs.h \
 /usr/include/c++/9/functional /usr/include/c++/9/tuple \
 /usr/include/c++/9/array /usr/include/c++/9/bits/uses_allocator.h \
 /usr/include/c++/9/bits/invoke.h /usr/include/c++/9/bits/refwrap.h \
 /usr/include/c++/9/bits/std_function.h /usr/include/c++/9/unordered_map \
 /usr/include/c++/9/ext/aligned_buffer.h \
 /usr/include/c++/9/bits/hashtable.h \
 /usr/include/c++/9/bits/hashtable_policy.h \
 /usr/include/c++/9/bits/node_handle.h /usr/include/c++/9/optional \
 /usr/include/c++/9/bits/enable_special_members.h \
 /usr/include/c++/9/bits/unordered_map.h \
 /usr/include/c++/9/bits/erase_if.h \
 /usr/include/c++/9/pstl/execution_defs.h /usr/include/c++/9/cstddef \
 /usr/include/c++/9/memory /usr/include/c++/9/bits/stl_raw_storage_iter.h \
 /usr/include/c++/9/ext/concurrence.h \
 /usr/include/c++/9/bits/unique_ptr.h \
 /usr/include/c++/9/bits/shared_ptr.h \
 /usr/include/c++/9/bits/shared_ptr_base.h \
 /usr/include/c++/9/bits/allocated_ptr.h \
 /usr/include/c++/9/bits/shared_ptr_atomic.h \
 /usr/include/c++/9/bits/atomic_base.h \
 /usr/include/c++/9/bits/atomic_lockfree_defines.h \
 /usr/include/c++/9/backward/auto_ptr.h \
 /usr/include/c++/9/pstl/glue_memory_defs.h \
 /usr/include/c++/9/forward_list /usr/include/c++/9/bits/forward_list.h \
 /usr/include/c++/9/bits/forward_list.tcc /usr/include/c++/9/map \
 /usr/include/c++/9/bits/stl_tree.h /usr/include/c++/9/bits/stl_map.h \
 /usr/include/c++/9/bits/stl_multimap.h /usr/include/c++/9/valarray \
 /usr/include/c++/9/bits/valarray_array.h \
 /usr/include/c++/9/bits/valarray_array.tcc \
 /usr/include/c++/9/bits/valarray_before.h \
 /usr/include/c++/9/bits/slice_array.h \
 /usr/include/c++/9/bits/valarray_after.h \
 /usr/include/c++/9/bits/gslice.h /usr/include/c++/9/bits/gslice_array.h \
 /usr/include/c++/9/bits/mask_array.h \
 /usr/include/c++/9/bits/indirect_array.h /usr/include/c++/9/version \
 /usr/include/c++/9/cassert /usr/include/c++/9/cstring \
 /usr/include/c++/9/filesystem /usr/include/c++/9/bits/fs_fwd.h \
 /usr/include/c++/9/chrono /usr/include/c++/9/ratio \
 /usr/include/c++/9/ctime /usr/include/c++/9/bits/parse_numbers.h \
 /usr/include/c++/9/bits/fs_path.h /usr/include/c++/9/locale \
 /usr/include/c++/9/bits/locale_facets_nonio.h \
 /usr/include/x86_64-linux-gnu/c++/9/bits/time_members.h \
 /usr/include/x86_64-linux-gnu/c++/9/bits/messages_members.h \
 /usr/include/libintl.h /usr/include/c++/9/bits/codecvt.h \
 /usr/include/c++/9/bits/locale_facets_nonio.tcc \
 /usr/include/c++/9/bits/locale_conv.h /usr/include/c++/9/iomanip \
 /usr/include/c++/9/bits/quoted_string.h /usr/include/c++/9/sstream \
 /usr/include/c++/9/bits/sstream.tcc /usr/include/c++/9/codecvt \
 /usr/include/c++/9/bits/fs_dir.h /usr/include/c++/9/bits/fs_ops.h \
 /usr/include/c++/9/numeric /usr/include/c++/9/bits/stl_numeric.h \
 /usr/include/c++/9/pstl/glue_numeric_defs.h /usr/include/c++/9/any \
 /home/<USER>/lsq/mini_tool/src/MCPClient.h \
 /home/<USER>/lsq/mini_tool/src/LogFilePackDialog.h \
 /usr/include/wx-3.0/wx/progdlg.h \
 /usr/include/wx-3.0/wx/generic/progdlgg.h \
 /home/<USER>/lsq/mini_tool/src/ShowJetCmdDialog.h \
 /usr/include/wx-3.0/wx/grid.h /usr/include/wx-3.0/wx/generic/grid.h \
 /usr/include/wx-3.0/wx/generic/grideditors.h \
 /usr/include/wx-3.0/wx/generic/gridctrl.h
