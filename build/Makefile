# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.26

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /home/<USER>/Downloads/cmake-3.26.3-linux-x86_64/bin/cmake

# The command to remove a file.
RM = /home/<USER>/Downloads/cmake-3.26.3-linux-x86_64/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/lsq/mini_tool

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/lsq/mini_tool/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake cache editor..."
	/home/<USER>/Downloads/cmake-3.26.3-linux-x86_64/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/home/<USER>/Downloads/cmake-3.26.3-linux-x86_64/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/home/<USER>/Downloads/cmake-3.26.3-linux-x86_64/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/home/<USER>/Downloads/cmake-3.26.3-linux-x86_64/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/home/<USER>/Downloads/cmake-3.26.3-linux-x86_64/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/home/<USER>/Downloads/cmake-3.26.3-linux-x86_64/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/home/<USER>/Downloads/cmake-3.26.3-linux-x86_64/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/home/<USER>/Downloads/cmake-3.26.3-linux-x86_64/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/lsq/mini_tool/build/CMakeFiles /home/<USER>/lsq/mini_tool/build//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/lsq/mini_tool/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named mini_tool

# Build rule for target.
mini_tool: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 mini_tool
.PHONY : mini_tool

# fast build rule for target.
mini_tool/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mini_tool.dir/build.make CMakeFiles/mini_tool.dir/build
.PHONY : mini_tool/fast

src/AiChatDialog.o: src/AiChatDialog.cpp.o
.PHONY : src/AiChatDialog.o

# target to build an object file
src/AiChatDialog.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mini_tool.dir/build.make CMakeFiles/mini_tool.dir/src/AiChatDialog.cpp.o
.PHONY : src/AiChatDialog.cpp.o

src/AiChatDialog.i: src/AiChatDialog.cpp.i
.PHONY : src/AiChatDialog.i

# target to preprocess a source file
src/AiChatDialog.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mini_tool.dir/build.make CMakeFiles/mini_tool.dir/src/AiChatDialog.cpp.i
.PHONY : src/AiChatDialog.cpp.i

src/AiChatDialog.s: src/AiChatDialog.cpp.s
.PHONY : src/AiChatDialog.s

# target to generate assembly for a file
src/AiChatDialog.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mini_tool.dir/build.make CMakeFiles/mini_tool.dir/src/AiChatDialog.cpp.s
.PHONY : src/AiChatDialog.cpp.s

src/ChangeConfigDialog.o: src/ChangeConfigDialog.cpp.o
.PHONY : src/ChangeConfigDialog.o

# target to build an object file
src/ChangeConfigDialog.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mini_tool.dir/build.make CMakeFiles/mini_tool.dir/src/ChangeConfigDialog.cpp.o
.PHONY : src/ChangeConfigDialog.cpp.o

src/ChangeConfigDialog.i: src/ChangeConfigDialog.cpp.i
.PHONY : src/ChangeConfigDialog.i

# target to preprocess a source file
src/ChangeConfigDialog.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mini_tool.dir/build.make CMakeFiles/mini_tool.dir/src/ChangeConfigDialog.cpp.i
.PHONY : src/ChangeConfigDialog.cpp.i

src/ChangeConfigDialog.s: src/ChangeConfigDialog.cpp.s
.PHONY : src/ChangeConfigDialog.s

# target to generate assembly for a file
src/ChangeConfigDialog.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mini_tool.dir/build.make CMakeFiles/mini_tool.dir/src/ChangeConfigDialog.cpp.s
.PHONY : src/ChangeConfigDialog.cpp.s

src/DeleteSourceDialog.o: src/DeleteSourceDialog.cpp.o
.PHONY : src/DeleteSourceDialog.o

# target to build an object file
src/DeleteSourceDialog.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mini_tool.dir/build.make CMakeFiles/mini_tool.dir/src/DeleteSourceDialog.cpp.o
.PHONY : src/DeleteSourceDialog.cpp.o

src/DeleteSourceDialog.i: src/DeleteSourceDialog.cpp.i
.PHONY : src/DeleteSourceDialog.i

# target to preprocess a source file
src/DeleteSourceDialog.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mini_tool.dir/build.make CMakeFiles/mini_tool.dir/src/DeleteSourceDialog.cpp.i
.PHONY : src/DeleteSourceDialog.cpp.i

src/DeleteSourceDialog.s: src/DeleteSourceDialog.cpp.s
.PHONY : src/DeleteSourceDialog.s

# target to generate assembly for a file
src/DeleteSourceDialog.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mini_tool.dir/build.make CMakeFiles/mini_tool.dir/src/DeleteSourceDialog.cpp.s
.PHONY : src/DeleteSourceDialog.cpp.s

src/LogFilePackDialog.o: src/LogFilePackDialog.cpp.o
.PHONY : src/LogFilePackDialog.o

# target to build an object file
src/LogFilePackDialog.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mini_tool.dir/build.make CMakeFiles/mini_tool.dir/src/LogFilePackDialog.cpp.o
.PHONY : src/LogFilePackDialog.cpp.o

src/LogFilePackDialog.i: src/LogFilePackDialog.cpp.i
.PHONY : src/LogFilePackDialog.i

# target to preprocess a source file
src/LogFilePackDialog.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mini_tool.dir/build.make CMakeFiles/mini_tool.dir/src/LogFilePackDialog.cpp.i
.PHONY : src/LogFilePackDialog.cpp.i

src/LogFilePackDialog.s: src/LogFilePackDialog.cpp.s
.PHONY : src/LogFilePackDialog.s

# target to generate assembly for a file
src/LogFilePackDialog.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mini_tool.dir/build.make CMakeFiles/mini_tool.dir/src/LogFilePackDialog.cpp.s
.PHONY : src/LogFilePackDialog.cpp.s

src/LoginDialog.o: src/LoginDialog.cpp.o
.PHONY : src/LoginDialog.o

# target to build an object file
src/LoginDialog.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mini_tool.dir/build.make CMakeFiles/mini_tool.dir/src/LoginDialog.cpp.o
.PHONY : src/LoginDialog.cpp.o

src/LoginDialog.i: src/LoginDialog.cpp.i
.PHONY : src/LoginDialog.i

# target to preprocess a source file
src/LoginDialog.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mini_tool.dir/build.make CMakeFiles/mini_tool.dir/src/LoginDialog.cpp.i
.PHONY : src/LoginDialog.cpp.i

src/LoginDialog.s: src/LoginDialog.cpp.s
.PHONY : src/LoginDialog.s

# target to generate assembly for a file
src/LoginDialog.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mini_tool.dir/build.make CMakeFiles/mini_tool.dir/src/LoginDialog.cpp.s
.PHONY : src/LoginDialog.cpp.s

src/MCPClient.o: src/MCPClient.cpp.o
.PHONY : src/MCPClient.o

# target to build an object file
src/MCPClient.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mini_tool.dir/build.make CMakeFiles/mini_tool.dir/src/MCPClient.cpp.o
.PHONY : src/MCPClient.cpp.o

src/MCPClient.i: src/MCPClient.cpp.i
.PHONY : src/MCPClient.i

# target to preprocess a source file
src/MCPClient.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mini_tool.dir/build.make CMakeFiles/mini_tool.dir/src/MCPClient.cpp.i
.PHONY : src/MCPClient.cpp.i

src/MCPClient.s: src/MCPClient.cpp.s
.PHONY : src/MCPClient.s

# target to generate assembly for a file
src/MCPClient.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mini_tool.dir/build.make CMakeFiles/mini_tool.dir/src/MCPClient.cpp.s
.PHONY : src/MCPClient.cpp.s

src/MainFrame.o: src/MainFrame.cpp.o
.PHONY : src/MainFrame.o

# target to build an object file
src/MainFrame.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mini_tool.dir/build.make CMakeFiles/mini_tool.dir/src/MainFrame.cpp.o
.PHONY : src/MainFrame.cpp.o

src/MainFrame.i: src/MainFrame.cpp.i
.PHONY : src/MainFrame.i

# target to preprocess a source file
src/MainFrame.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mini_tool.dir/build.make CMakeFiles/mini_tool.dir/src/MainFrame.cpp.i
.PHONY : src/MainFrame.cpp.i

src/MainFrame.s: src/MainFrame.cpp.s
.PHONY : src/MainFrame.s

# target to generate assembly for a file
src/MainFrame.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mini_tool.dir/build.make CMakeFiles/mini_tool.dir/src/MainFrame.cpp.s
.PHONY : src/MainFrame.cpp.s

src/ProgressDialog.o: src/ProgressDialog.cpp.o
.PHONY : src/ProgressDialog.o

# target to build an object file
src/ProgressDialog.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mini_tool.dir/build.make CMakeFiles/mini_tool.dir/src/ProgressDialog.cpp.o
.PHONY : src/ProgressDialog.cpp.o

src/ProgressDialog.i: src/ProgressDialog.cpp.i
.PHONY : src/ProgressDialog.i

# target to preprocess a source file
src/ProgressDialog.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mini_tool.dir/build.make CMakeFiles/mini_tool.dir/src/ProgressDialog.cpp.i
.PHONY : src/ProgressDialog.cpp.i

src/ProgressDialog.s: src/ProgressDialog.cpp.s
.PHONY : src/ProgressDialog.s

# target to generate assembly for a file
src/ProgressDialog.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mini_tool.dir/build.make CMakeFiles/mini_tool.dir/src/ProgressDialog.cpp.s
.PHONY : src/ProgressDialog.cpp.s

src/ReplaceAlgorithmDialog.o: src/ReplaceAlgorithmDialog.cpp.o
.PHONY : src/ReplaceAlgorithmDialog.o

# target to build an object file
src/ReplaceAlgorithmDialog.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mini_tool.dir/build.make CMakeFiles/mini_tool.dir/src/ReplaceAlgorithmDialog.cpp.o
.PHONY : src/ReplaceAlgorithmDialog.cpp.o

src/ReplaceAlgorithmDialog.i: src/ReplaceAlgorithmDialog.cpp.i
.PHONY : src/ReplaceAlgorithmDialog.i

# target to preprocess a source file
src/ReplaceAlgorithmDialog.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mini_tool.dir/build.make CMakeFiles/mini_tool.dir/src/ReplaceAlgorithmDialog.cpp.i
.PHONY : src/ReplaceAlgorithmDialog.cpp.i

src/ReplaceAlgorithmDialog.s: src/ReplaceAlgorithmDialog.cpp.s
.PHONY : src/ReplaceAlgorithmDialog.s

# target to generate assembly for a file
src/ReplaceAlgorithmDialog.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mini_tool.dir/build.make CMakeFiles/mini_tool.dir/src/ReplaceAlgorithmDialog.cpp.s
.PHONY : src/ReplaceAlgorithmDialog.cpp.s

src/ShowJetCmdDialog.o: src/ShowJetCmdDialog.cpp.o
.PHONY : src/ShowJetCmdDialog.o

# target to build an object file
src/ShowJetCmdDialog.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mini_tool.dir/build.make CMakeFiles/mini_tool.dir/src/ShowJetCmdDialog.cpp.o
.PHONY : src/ShowJetCmdDialog.cpp.o

src/ShowJetCmdDialog.i: src/ShowJetCmdDialog.cpp.i
.PHONY : src/ShowJetCmdDialog.i

# target to preprocess a source file
src/ShowJetCmdDialog.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mini_tool.dir/build.make CMakeFiles/mini_tool.dir/src/ShowJetCmdDialog.cpp.i
.PHONY : src/ShowJetCmdDialog.cpp.i

src/ShowJetCmdDialog.s: src/ShowJetCmdDialog.cpp.s
.PHONY : src/ShowJetCmdDialog.s

# target to generate assembly for a file
src/ShowJetCmdDialog.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mini_tool.dir/build.make CMakeFiles/mini_tool.dir/src/ShowJetCmdDialog.cpp.s
.PHONY : src/ShowJetCmdDialog.cpp.s

src/SystemInfoDialog.o: src/SystemInfoDialog.cpp.o
.PHONY : src/SystemInfoDialog.o

# target to build an object file
src/SystemInfoDialog.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mini_tool.dir/build.make CMakeFiles/mini_tool.dir/src/SystemInfoDialog.cpp.o
.PHONY : src/SystemInfoDialog.cpp.o

src/SystemInfoDialog.i: src/SystemInfoDialog.cpp.i
.PHONY : src/SystemInfoDialog.i

# target to preprocess a source file
src/SystemInfoDialog.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mini_tool.dir/build.make CMakeFiles/mini_tool.dir/src/SystemInfoDialog.cpp.i
.PHONY : src/SystemInfoDialog.cpp.i

src/SystemInfoDialog.s: src/SystemInfoDialog.cpp.s
.PHONY : src/SystemInfoDialog.s

# target to generate assembly for a file
src/SystemInfoDialog.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mini_tool.dir/build.make CMakeFiles/mini_tool.dir/src/SystemInfoDialog.cpp.s
.PHONY : src/SystemInfoDialog.cpp.s

src/UdpTestDialog.o: src/UdpTestDialog.cpp.o
.PHONY : src/UdpTestDialog.o

# target to build an object file
src/UdpTestDialog.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mini_tool.dir/build.make CMakeFiles/mini_tool.dir/src/UdpTestDialog.cpp.o
.PHONY : src/UdpTestDialog.cpp.o

src/UdpTestDialog.i: src/UdpTestDialog.cpp.i
.PHONY : src/UdpTestDialog.i

# target to preprocess a source file
src/UdpTestDialog.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mini_tool.dir/build.make CMakeFiles/mini_tool.dir/src/UdpTestDialog.cpp.i
.PHONY : src/UdpTestDialog.cpp.i

src/UdpTestDialog.s: src/UdpTestDialog.cpp.s
.PHONY : src/UdpTestDialog.s

# target to generate assembly for a file
src/UdpTestDialog.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mini_tool.dir/build.make CMakeFiles/mini_tool.dir/src/UdpTestDialog.cpp.s
.PHONY : src/UdpTestDialog.cpp.s

src/main.o: src/main.cpp.o
.PHONY : src/main.o

# target to build an object file
src/main.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mini_tool.dir/build.make CMakeFiles/mini_tool.dir/src/main.cpp.o
.PHONY : src/main.cpp.o

src/main.i: src/main.cpp.i
.PHONY : src/main.i

# target to preprocess a source file
src/main.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mini_tool.dir/build.make CMakeFiles/mini_tool.dir/src/main.cpp.i
.PHONY : src/main.cpp.i

src/main.s: src/main.cpp.s
.PHONY : src/main.s

# target to generate assembly for a file
src/main.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mini_tool.dir/build.make CMakeFiles/mini_tool.dir/src/main.cpp.s
.PHONY : src/main.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... mini_tool"
	@echo "... src/AiChatDialog.o"
	@echo "... src/AiChatDialog.i"
	@echo "... src/AiChatDialog.s"
	@echo "... src/ChangeConfigDialog.o"
	@echo "... src/ChangeConfigDialog.i"
	@echo "... src/ChangeConfigDialog.s"
	@echo "... src/DeleteSourceDialog.o"
	@echo "... src/DeleteSourceDialog.i"
	@echo "... src/DeleteSourceDialog.s"
	@echo "... src/LogFilePackDialog.o"
	@echo "... src/LogFilePackDialog.i"
	@echo "... src/LogFilePackDialog.s"
	@echo "... src/LoginDialog.o"
	@echo "... src/LoginDialog.i"
	@echo "... src/LoginDialog.s"
	@echo "... src/MCPClient.o"
	@echo "... src/MCPClient.i"
	@echo "... src/MCPClient.s"
	@echo "... src/MainFrame.o"
	@echo "... src/MainFrame.i"
	@echo "... src/MainFrame.s"
	@echo "... src/ProgressDialog.o"
	@echo "... src/ProgressDialog.i"
	@echo "... src/ProgressDialog.s"
	@echo "... src/ReplaceAlgorithmDialog.o"
	@echo "... src/ReplaceAlgorithmDialog.i"
	@echo "... src/ReplaceAlgorithmDialog.s"
	@echo "... src/ShowJetCmdDialog.o"
	@echo "... src/ShowJetCmdDialog.i"
	@echo "... src/ShowJetCmdDialog.s"
	@echo "... src/SystemInfoDialog.o"
	@echo "... src/SystemInfoDialog.i"
	@echo "... src/SystemInfoDialog.s"
	@echo "... src/UdpTestDialog.o"
	@echo "... src/UdpTestDialog.i"
	@echo "... src/UdpTestDialog.s"
	@echo "... src/main.o"
	@echo "... src/main.i"
	@echo "... src/main.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

