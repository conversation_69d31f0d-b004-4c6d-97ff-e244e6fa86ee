#include "ShowJetCmdDialog.h"
#include <wx/sizer.h>
#include <wx/statbox.h>
#include <wx/msgdlg.h>
#include <cstring>

// 字节序转换函数：将网络字节序转换为主机字节序
static uint32_t BigEndianToHost32(uint32_t value)
{
    return ((value & 0xFF000000) >> 24) |
           ((value & 0x00FF0000) >> 8)  |
           ((value & 0x0000FF00) << 8)  |
           ((value & 0x000000FF) << 24);
}

wxBEGIN_EVENT_TABLE(ShowJetCmdDialog, wxDialog)
    EVT_BUTTON(ID_JET_CONNECT_BTN, ShowJetCmdDialog::OnConnect)
    EVT_BUTTON(ID_JET_START_BTN, ShowJetCmdDialog::OnStart)
    EVT_BUTTON(ID_JET_STOP_BTN, ShowJetCmdDialog::OnStop)
    EVT_CLOSE(ShowJetCmdDialog::OnClose)
    EVT_TIMER(ID_JET_DISPLAY_TIMER, ShowJetCmdDialog::OnDisplayTimer)
    EVT_SOCKET(ID_JET_SOCKET_EVENT, ShowJetCmdDialog::OnSocketEvent)
    EVT_SPINCTRL(ID_JET_VALVE_COUNT_SPIN, ShowJetCmdDialog::OnValveCountChanged)
wxEND_EVENT_TABLE()

ShowJetCmdDialog::ShowJetCmdDialog(wxWindow* parent)
    : wxDialog(parent, wxID_ANY, wxT("显示喷吹指令"), wxDefaultPosition, wxSize(1800, 800))
    , m_socket(nullptr)
    , m_connected(false)
    , m_displayTimer(nullptr)
    , m_currentDisplayRow(0)
    , m_valveCount(90)
    , m_isDisplaying(false)
    , m_receivedPacketCount(0)
{
    CreateControls();
    
    // 创建定时器
    m_displayTimer = new wxTimer(this, ID_JET_DISPLAY_TIMER);
    
    // 设置默认值
    m_localIpText->SetValue(wxT("**************"));
    m_localPortSpin->SetValue(1234);
    m_remoteIpText->SetValue(wxT("***************"));
    m_remotePortSpin->SetValue(1234);
    
    m_valveCountSpin->SetValue(90);
    
    // 初始化数据
    ClearData();
    InitializeDisplayGrid();
    
    UpdateStatus(wxT("未连接"));
}

ShowJetCmdDialog::~ShowJetCmdDialog()
{
    if (m_displayTimer) {
        m_displayTimer->Stop();
        delete m_displayTimer;
    }
    
    DisconnectSocket();
}

void ShowJetCmdDialog::CreateControls()
{
    wxBoxSizer* mainSizer = new wxBoxSizer(wxVERTICAL);
    
    // 网络连接设置
    wxStaticBoxSizer* networkBox = new wxStaticBoxSizer(wxVERTICAL, this, wxT("网络连接"));

    // 网络设置行（本机和远端设置合并到一行）
    wxBoxSizer* networkSizer = new wxBoxSizer(wxHORIZONTAL);

    // 本机设置
    networkSizer->Add(new wxStaticText(this, wxID_ANY, wxT("本机IP:")), 0, wxALIGN_CENTER_VERTICAL | wxRIGHT, 5);
    m_localIpText = new wxTextCtrl(this, wxID_ANY, wxEmptyString, wxDefaultPosition, wxSize(140, -1));
    networkSizer->Add(m_localIpText, 0, wxRIGHT, 25);

    networkSizer->Add(new wxStaticText(this, wxID_ANY, wxT("端口:")), 0, wxALIGN_CENTER_VERTICAL | wxRIGHT, 5);
    m_localPortSpin = new wxSpinCtrl(this, wxID_ANY, wxEmptyString, wxDefaultPosition, wxSize(140, -1),
                                     wxSP_ARROW_KEYS, 1, 65535, 9020);
    networkSizer->Add(m_localPortSpin, 0, wxRIGHT, 25);

    // 远端设置
    networkSizer->Add(new wxStaticText(this, wxID_ANY, wxT("远端IP:")), 0, wxALIGN_CENTER_VERTICAL | wxRIGHT, 5);
    m_remoteIpText = new wxTextCtrl(this, wxID_ANY, wxEmptyString, wxDefaultPosition, wxSize(140, -1));
    networkSizer->Add(m_remoteIpText, 0, wxRIGHT, 25);

    networkSizer->Add(new wxStaticText(this, wxID_ANY, wxT("端口:")), 0, wxALIGN_CENTER_VERTICAL | wxRIGHT, 5);
    m_remotePortSpin = new wxSpinCtrl(this, wxID_ANY, wxEmptyString, wxDefaultPosition, wxSize(140, -1),
                                      wxSP_ARROW_KEYS, 1, 65535, 9020);
    networkSizer->Add(m_remotePortSpin, 0, wxRIGHT, 25);

    // 连接按钮和状态显示
    m_connectBtn = new wxButton(this, ID_JET_CONNECT_BTN, wxT("连接"));
    networkSizer->Add(m_connectBtn, 0, wxRIGHT, 10);

    m_statusText = new wxStaticText(this, wxID_ANY, wxT("未连接"));
    networkSizer->Add(m_statusText, 1, wxALIGN_CENTER_VERTICAL);

    networkBox->Add(networkSizer, 0, wxEXPAND | wxALL, 5);

    // 控制参数设置
    wxStaticBoxSizer* controlBox = new wxStaticBoxSizer(wxVERTICAL, this, wxT("显示控制"));

    wxBoxSizer* paramSizer = new wxBoxSizer(wxHORIZONTAL);

    // 阀门数量
    paramSizer->Add(new wxStaticText(this, wxID_ANY, wxT("阀门数量:")), 0, wxALIGN_CENTER_VERTICAL | wxRIGHT, 5);
    m_valveCountSpin = new wxSpinCtrl(this, ID_JET_VALVE_COUNT_SPIN, wxEmptyString, wxDefaultPosition, wxSize(150, -1),
                                      wxSP_ARROW_KEYS, 1, 360, 90);
    paramSizer->Add(m_valveCountSpin, 0, wxRIGHT, 15);

    // 控制按钮
    m_startBtn = new wxButton(this, ID_JET_START_BTN, wxT("开始显示"));
    m_stopBtn = new wxButton(this, ID_JET_STOP_BTN, wxT("停止显示"));
    m_startBtn->Enable(false);
    m_stopBtn->Enable(false);

    paramSizer->Add(m_startBtn, 0, wxRIGHT, 10);
    paramSizer->Add(m_stopBtn, 0, wxRIGHT, 15);

    // 状态显示
    m_receivedPacketsLabel = new wxStaticText(this, wxID_ANY, wxT("接收包数: 0"));
    paramSizer->Add(m_receivedPacketsLabel, 0, wxALIGN_CENTER_VERTICAL);

    controlBox->Add(paramSizer, 0, wxEXPAND | wxALL, 5);

    // 将网络设置和控制设置放在同一行
    wxBoxSizer* topSizer = new wxBoxSizer(wxHORIZONTAL);
    topSizer->Add(networkBox, 1, wxEXPAND | wxRIGHT, 5);
    topSizer->Add(controlBox, 1, wxEXPAND | wxLEFT, 5);

    mainSizer->Add(topSizer, 0, wxEXPAND | wxALL, 5);

    // 显示网格
    wxStaticBoxSizer* displayBox = new wxStaticBoxSizer(wxVERTICAL, this, wxT("喷吹指令显示"));
    
    m_displayGrid = new wxGrid(this, wxID_ANY);
    m_displayGrid->CreateGrid(90, 125); // 初始创建90行125列（行列互换）
    m_displayGrid->SetRowLabelSize(90);  // 隐藏行号
    m_displayGrid->SetColLabelSize(0);  // 隐藏列号
    m_displayGrid->SetDefaultRowSize(20);  // 更小的行高
    m_displayGrid->SetDefaultColSize(7);  // 更小的列宽
    m_displayGrid->EnableGridLines(false);
    m_displayGrid->EnableEditing(false);
    
    displayBox->Add(m_displayGrid, 1, wxEXPAND | wxALL, 5);
    
    mainSizer->Add(displayBox, 1, wxEXPAND | wxALL, 5);

    // 日志显示
    wxStaticBoxSizer* logBox = new wxStaticBoxSizer(wxVERTICAL, this, wxT("日志"));
    m_logText = new wxTextCtrl(this, wxID_ANY, wxEmptyString, wxDefaultPosition, wxSize(-1, 80),
                               wxTE_MULTILINE | wxTE_READONLY);
    logBox->Add(m_logText, 1, wxEXPAND | wxALL, 5);
    
    mainSizer->Add(logBox, 0, wxEXPAND | wxALL, 5);
    
    // 关闭按钮
    wxBoxSizer* closeSizer = new wxBoxSizer(wxHORIZONTAL);
    closeSizer->AddStretchSpacer();
    wxButton* closeBtn = new wxButton(this, wxID_CLOSE, wxT("关闭"));
    closeBtn->Bind(wxEVT_COMMAND_BUTTON_CLICKED, [this](wxCommandEvent&) {
        Close();
    });
    closeSizer->Add(closeBtn, 0);

    mainSizer->Add(closeSizer, 0, wxEXPAND | wxALL, 5);

    SetSizer(mainSizer);
    SetMinSize(wxSize(1800, 800));
}

void ShowJetCmdDialog::OnConnect(wxCommandEvent& WXUNUSED(event))
{
    if (!m_connected) {
        if (ConnectSocket()) {
            m_connectBtn->SetLabel(wxT("断开"));
            m_startBtn->Enable(true);
            UpdateStatus(wxT("已连接"));
            LogMessage(wxT("UDP连接成功"));
        } else {
            UpdateStatus(wxT("连接失败"));
            LogMessage(wxT("UDP连接失败"));
        }
    } else {
        DisconnectSocket();
        m_connectBtn->SetLabel(wxT("连接"));
        m_startBtn->Enable(false);
        m_stopBtn->Enable(false);
        UpdateStatus(wxT("已断开"));
        LogMessage(wxT("UDP连接已断开"));
    }
}

void ShowJetCmdDialog::OnStart(wxCommandEvent& WXUNUSED(event))
{
    if (!m_connected) {
        wxMessageBox(wxT("请先连接UDP"), wxT("错误"), wxOK | wxICON_ERROR);
        return;
    }

    StartDisplay();
}

void ShowJetCmdDialog::OnStop(wxCommandEvent& WXUNUSED(event))
{
    StopDisplay();
}

void ShowJetCmdDialog::OnClose(wxCloseEvent& event)
{
    StopDisplay();
    DisconnectSocket();
    event.Skip();
}

void ShowJetCmdDialog::OnDisplayTimer(wxTimerEvent& WXUNUSED(event))
{
    if (!m_isDisplaying || m_jetCommandData.empty()) {
        return;
    }

    // 移动到下一行
    m_currentDisplayRow++;
    if (m_currentDisplayRow >= 125) {
        m_currentDisplayRow = 0; // 循环显示
    }

    // 更新显示网格
    UpdateDisplayGrid();
}

void ShowJetCmdDialog::OnSocketEvent(wxSocketEvent& event)
{
    switch (event.GetSocketEvent()) {
        case wxSOCKET_INPUT:
            {
                // 处理接收到的数据
                uint8_t buffer[1500];  // UDP最大数据包大小
                wxIPV4address addr;

                m_socket->RecvFrom(addr, buffer, sizeof(buffer));
                size_t bytesRead = m_socket->LastCount();

                if (bytesRead > 0) {
                    LogMessage(wxString::Format(wxT("收到数据包，大小: %zu 字节"), bytesRead));

                    // 解析阀数据包
                    ParseValveDataPacket(buffer, bytesRead);
                }
            }
            break;
        case wxSOCKET_LOST:
            LogMessage(wxT("连接丢失"));
            DisconnectSocket();
            break;
        default:
            break;
    }
}

void ShowJetCmdDialog::OnValveCountChanged(wxSpinEvent& WXUNUSED(event))
{
    m_valveCount = m_valveCountSpin->GetValue();
    InitializeDisplayGrid();
    ClearData();
    LogMessage(wxString::Format(wxT("阀门数量设置为: %d"), m_valveCount));
}

void ShowJetCmdDialog::UpdateStatus(const wxString& status)
{
    m_statusText->SetLabel(status);
}

void ShowJetCmdDialog::LogMessage(const wxString& message)
{
    wxDateTime now = wxDateTime::Now();
    wxString timeStr = now.Format(wxT("%H:%M:%S"));

    // 获取毫秒部分
    wxLongLong nowMs = wxGetLocalTimeMillis();
    int milliseconds = (nowMs % 1000).ToLong();

    // 格式化为 HH:MM:SS.mmm
    wxString timeWithMs = wxString::Format(wxT("%s.%03d"), timeStr, milliseconds);

    m_logText->AppendText(wxString::Format(wxT("[%s] %s\n"), timeWithMs, message));

    // 限制日志行数，防止内存占用过多
    const int MAX_LOG_LINES = 1000;
    int lineCount = m_logText->GetNumberOfLines();

    if (lineCount > MAX_LOG_LINES) {
        int linesToRemove = lineCount - MAX_LOG_LINES + 100;

        long pos = 0;
        for (int i = 0; i < linesToRemove; i++) {
            pos = m_logText->GetValue().find('\n', pos);
            if (pos == wxString::npos) break;
            pos++;
        }

        if (pos != wxString::npos && pos > 0) {
            m_logText->Remove(0, pos);
        }
    }
}

bool ShowJetCmdDialog::ConnectSocket()
{
    if (m_socket) {
        DisconnectSocket();
    }

    // 设置本地地址
    if (!m_localAddr.Hostname(m_localIpText->GetValue()) ||
        !m_localAddr.Service(m_localPortSpin->GetValue())) {
        LogMessage(wxT("本地地址设置失败"));
        return false;
    }

    // 设置远程地址
    if (!m_remoteAddr.Hostname(m_remoteIpText->GetValue()) ||
        !m_remoteAddr.Service(m_remotePortSpin->GetValue())) {
        LogMessage(wxT("远程地址设置失败"));
        return false;
    }

    // 创建UDP socket
    m_socket = new wxDatagramSocket(m_localAddr, wxSOCKET_NOWAIT);
    if (!m_socket->IsOk()) {
        LogMessage(wxT("创建UDP socket失败"));
        delete m_socket;
        m_socket = nullptr;
        return false;
    }

    // 设置事件处理
    m_socket->SetEventHandler(*this, ID_JET_SOCKET_EVENT);
    m_socket->SetNotify(wxSOCKET_INPUT_FLAG | wxSOCKET_LOST_FLAG);
    m_socket->Notify(true);

    m_connected = true;
    return true;
}

void ShowJetCmdDialog::DisconnectSocket()
{
    if (m_socket) {
        m_socket->Destroy();
        m_socket = nullptr;
    }
    m_connected = false;
}

void ShowJetCmdDialog::ParseValveDataPacket(const uint8_t* data, size_t dataSize)
{
    if (dataSize < 32) { // 至少需要32字节的头部
        LogMessage(wxT("数据包太小，无法解析"));
        return;
    }

    // 解析包头
    ValveDataPacket packet;
    memcpy(&packet, data, wxMin(dataSize, sizeof(packet)));

    // 转换字节序
    packet.packetType = BigEndianToHost32(packet.packetType);
    packet.packetSize = BigEndianToHost32(packet.packetSize);
    packet.blowDelay = BigEndianToHost32(packet.blowDelay);
    packet.triggerTime = BigEndianToHost32(packet.triggerTime);
    packet.totalPackets = BigEndianToHost32(packet.totalPackets);
    packet.udp_send_num = BigEndianToHost32(packet.udp_send_num);

    // 检查是否是阀数据包（包类型1-7）
    if (packet.packetType < 1 || packet.packetType > 7) {
        LogMessage(wxString::Format(wxT("收到非阀数据包，类型: %u"), packet.packetType));
        return;
    }

    LogMessage(wxString::Format(wxT("收到第%u包阀数据，UDP序号: %u"),
                               packet.packetType, packet.udp_send_num));

    // 提取喷吹指令
    ExtractValveCommands(packet);

    // 更新接收包数
    m_receivedPacketCount++;
    m_receivedPacketsLabel->SetLabel(wxString::Format(wxT("接收包数: %d"), m_receivedPacketCount));
}

void ShowJetCmdDialog::ExtractValveCommands(const ValveDataPacket& packet)
{
    // 计算当前包的阀范围
    int valveStart = (packet.packetType - 1) * 80 + 1;  // 从1开始
    int valveEnd = wxMin((int)packet.packetType * 80, m_valveCount);

    if (valveStart > m_valveCount) {
        return; // 超出范围
    }

    // 确保数据结构已初始化
    if (m_jetCommandData.empty()) {
        m_jetCommandData.resize(125, std::vector<bool>(m_valveCount, false));
    }

    // 解析每个阀的数据
    for (int valve = valveStart; valve <= valveEnd; valve++) {
        int valveIndex = valve - valveStart; // 在当前包中的索引
        if (valveIndex >= 80) break; // 安全检查

        const uint8_t* valveData = packet.valveData + valveIndex * 16;

        // 解析125位数据（16字节，前125位有效）
        for (int row = 0; row < 125; row++) {
            int byteIndex = row / 8;
            int bitIndex = row % 8;

            if (byteIndex < 16) {
                bool bitValue = (valveData[byteIndex] >> (7 - bitIndex)) & 1;
                m_jetCommandData[row][valve - 1] = bitValue; // 转换为0基索引
            }
        }
    }

    LogMessage(wxString::Format(wxT("解析阀%d到阀%d的喷吹指令"), valveStart, valveEnd));
}

void ShowJetCmdDialog::InitializeDisplayGrid()
{
    // 清除现有网格
    if (m_displayGrid->GetNumberRows() > 0) {
        m_displayGrid->DeleteRows(0, m_displayGrid->GetNumberRows());
    }
    if (m_displayGrid->GetNumberCols() > 0) {
        m_displayGrid->DeleteCols(0, m_displayGrid->GetNumberCols());
    }

    // 创建新的网格：行数为阀门数量，125列用于时间点显示（行列互换）
    m_displayGrid->AppendRows(m_valveCount);
    m_displayGrid->SetRowLabelSize(m_valveCount);
    m_displayGrid->AppendCols(125);

    // 设置单元格大小 - 更紧密的显示
    for (int row = 0; row < m_valveCount; row++) {
        m_displayGrid->SetRowSize(row, 20);
    }
    for (int col = 0; col < 125; col++) {
        m_displayGrid->SetColSize(col, 7);
    }
}

void ShowJetCmdDialog::UpdateDisplayGrid()
{
    if (m_jetCommandData.empty()) {
        return;
    }

    // 显示所有数据（行列互换：行=阀门，列=时间点）
    for (int row = 0; row < m_valveCount && row < m_displayGrid->GetNumberRows(); row++) {
        for (int col = 0; col < 125 && col < m_displayGrid->GetNumberCols(); col++) {
            wxString cellValue = m_jetCommandData[col][row] ? wxT("1") : wxT("0");
            m_displayGrid->SetCellValue(row, col, cellValue);

            // 设置背景颜色
            m_displayGrid->SetCellBackgroundColour(row, col,
                m_jetCommandData[col][row] ? wxColour(255, 200, 200) : wxColour(240, 240, 240));
        }
    }

    m_displayGrid->ForceRefresh();
}

void ShowJetCmdDialog::StartDisplay()
{
    if (m_jetCommandData.empty()) {
        wxMessageBox(wxT("没有接收到喷吹指令数据"), wxT("提示"), wxOK | wxICON_WARNING);
        return;
    }

    m_isDisplaying = true;
    m_currentDisplayRow = 0;

    m_startBtn->Enable(false);
    m_stopBtn->Enable(true);

    UpdateDisplayGrid();
    // 开始定时器，每800微秒显示下一行
    m_displayTimer->Start(50); // 1ms，实际应该是0.8ms，但wxTimer最小间隔是1ms

    LogMessage(wxT("开始流水显示喷吹指令"));
    
}

void ShowJetCmdDialog::StopDisplay()
{
    m_isDisplaying = false;
    m_displayTimer->Stop();

    m_startBtn->Enable(true);
    m_stopBtn->Enable(false);

    LogMessage(wxT("停止显示喷吹指令"));
}

void ShowJetCmdDialog::ClearData()
{
    m_jetCommandData.clear();
    m_jetCommandData.resize(125, std::vector<bool>(m_valveCount, false));
    m_currentDisplayRow = 0;
    m_receivedPacketCount = 0;

    m_receivedPacketsLabel->SetLabel(wxT("接收包数: 0"));

    // 清空显示网格
    for (int row = 0; row < m_displayGrid->GetNumberRows(); row++) {
        for (int col = 0; col < m_displayGrid->GetNumberCols(); col++) {
            m_displayGrid->SetCellValue(row, col, wxT(""));
            m_displayGrid->SetCellBackgroundColour(row, col, wxColour(255, 255, 255));
        }
    }
    m_displayGrid->ForceRefresh();
}
