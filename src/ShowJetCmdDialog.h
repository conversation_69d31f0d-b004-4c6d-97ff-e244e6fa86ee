#ifndef SHOWJETCMDDIALOG_H
#define SHOWJETCMDDIALOG_H

#include <wx/wx.h>
#include <wx/socket.h>
#include <wx/timer.h>
#include <wx/spinctrl.h>
#include <wx/statbox.h>
#include <wx/grid.h>
#include <vector>

class ShowJetCmdDialog : public wxDialog
{
public:
    ShowJetCmdDialog(wxWindow* parent);
    virtual ~ShowJetCmdDialog();

private:
    // UI控件
    wxTextCtrl* m_localIpText;
    wxSpinCtrl* m_localPortSpin;
    wxTextCtrl* m_remoteIpText;
    wxSpinCtrl* m_remotePortSpin;
    wxButton* m_connectBtn;
    wxStaticText* m_statusText;
    
    // 阀门数量设置
    wxSpinCtrl* m_valveCountSpin;
    wxButton* m_startBtn;
    wxButton* m_stopBtn;
    
    // 显示控件
    wxGrid* m_displayGrid;
    wxStaticText* m_receivedPacketsLabel;
    
    // 日志显示
    wxTextCtrl* m_logText;
    
    // 网络相关
    wxDatagramSocket* m_socket;
    wxIPV4address m_localAddr;
    wxIPV4address m_remoteAddr;
    bool m_connected;
    
    // 定时器
    wxTimer* m_displayTimer;
    
    // 数据存储
    std::vector<std::vector<bool>> m_jetCommandData; // [row][valve] 125行，每行存储各阀的0/1状态
    std::vector<std::vector<bool>> m_lastDisplayData; // 上次显示的数据，用于增量更新
    int m_currentDisplayRow;
    int m_valveCount;
    bool m_isDisplaying;
    int m_receivedPacketCount;

    // 性能优化相关
    int m_updateInterval; // 动态更新间隔
    bool m_useIncrementalUpdate; // 是否使用增量更新
    
    // 阀数据包结构体（复用UdpTestDialog的定义）
    struct ValveDataPacket {
        uint32_t packetType;      // 1-7表示第几包阀数据
        uint32_t packetSize;      // 整个包的大小长度
        uint32_t blowDelay;       // 喷吹延时X
        uint32_t triggerTime;     // 行触发时间(us)
        uint32_t totalPackets;    // 数据总包数
        uint32_t udp_send_num;    // UDP发送序号     
        uint8_t reserved[8];      // 空字节
        uint8_t valveData[80 * 16]; // 阀数据，每个阀16字节
    };
    
    // 事件处理
    void OnConnect(wxCommandEvent& event);
    void OnStart(wxCommandEvent& event);
    void OnStop(wxCommandEvent& event);
    void OnClose(wxCloseEvent& event);
    void OnDisplayTimer(wxTimerEvent& event);
    void OnSocketEvent(wxSocketEvent& event);
    void OnValveCountChanged(wxSpinEvent& event);
    
    // 辅助函数
    void CreateControls();
    void UpdateStatus(const wxString& status);
    void LogMessage(const wxString& message);
    bool ConnectSocket();
    void DisconnectSocket();
    void ParseValveDataPacket(const uint8_t* data, size_t dataSize);
    void ExtractValveCommands(const ValveDataPacket& packet);
    void InitializeDisplayGrid();
    void UpdateDisplayGrid();
    void UpdateDisplayGridFull();
    void UpdateDisplayGridIncremental();
    void StartDisplay();
    void StopDisplay();
    void ClearData();
    
    DECLARE_EVENT_TABLE()
};

// 事件ID
enum
{
    ID_JET_CONNECT_BTN = 3000,
    ID_JET_START_BTN,
    ID_JET_STOP_BTN,
    ID_JET_DISPLAY_TIMER,
    ID_JET_SOCKET_EVENT,
    ID_JET_VALVE_COUNT_SPIN
};

#endif // SHOWJETCMDDIALOG_H
