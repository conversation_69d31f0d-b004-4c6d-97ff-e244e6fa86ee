# 显示喷吹指令功能说明

## 功能概述

新增的"显示喷吹指令"功能可以通过UDP接收喷吹指令数据，并以流水显示的方式实时展示每个阀门的开关状态。

## 功能特点

1. **UDP通信接收**：接收符合协议规范的阀数据包
2. **实时解析**：解析每个阀门的125行喷吹指令数据
3. **流水显示**：按照800us的间隔逐行显示喷吹指令
4. **可配置阀门数量**：用户可以设置1-560个阀门
5. **网格显示**：使用表格形式显示，每行显示所有阀门的0/1状态

## 使用方法

### 1. 启动程序
运行mini_tool程序，点击"显示喷吹命令"按钮。

### 2. 网络设置
- **本机IP**：设置为**************（默认）
- **本机端口**：设置为9020（默认）
- **远端IP**：设置为**************（默认）
- **远端端口**：设置为9020（默认）

### 3. 参数配置
- **阀门数量**：设置需要显示的阀门数量（1-560）

### 4. 连接和显示
1. 点击"连接"按钮建立UDP连接
2. 等待接收喷吹指令数据包
3. 点击"开始显示"按钮开始流水显示
4. 点击"停止显示"按钮停止显示

## 显示说明

### 网格显示
- **行**：表示时间序列，每行代表一个时间点（800us间隔）
- **列**：表示阀门编号，从1开始
- **内容**：0表示阀门关闭，1表示阀门开启
- **高亮**：当前显示行会用不同颜色高亮显示

### 状态信息
- **当前行**：显示当前正在显示的行号（1-125）
- **接收包数**：显示已接收的UDP数据包数量

## 数据格式

### UDP数据包格式
根据protocol.md文档，程序接收以下格式的阀数据包：

- **包类型**：1-7（表示第几包阀数据）
- **包大小**：32字节头部 + 阀数据
- **喷吹延时**：X*800us
- **触发时间**：800us
- **总包数**：根据阀门数量计算
- **阀数据**：每个阀16字节，前125位有效

### 数据解析
- 每个阀门有125行数据（对应125个时间点）
- 每行数据表示该时间点阀门的开关状态
- 数据按位存储，1表示开启，0表示关闭

## 测试方法

### 使用测试程序
提供了test_udp_sender.py测试程序：

```bash
python3 test_udp_sender.py [目标IP] [目标端口] [阀门数量]
```

例如：
```bash
python3 test_udp_sender.py ************** 9020 90
```

### 测试步骤
1. 启动mini_tool程序，打开"显示喷吹命令"对话框
2. 配置网络参数和阀门数量
3. 点击"连接"
4. 运行测试程序发送数据
5. 在对话框中点击"开始显示"观察效果

## 编译和运行

### 编译程序
```bash
cd build
make
```

### 运行主程序
```bash
./mini_tool
```

### 运行测试发送程序
```bash
python3 test_udp_sender.py 127.0.0.1 9020 20
```

## 技术实现

### 关键技术点
1. **UDP Socket通信**：使用wxDatagramSocket接收数据
2. **数据包解析**：按照协议格式解析二进制数据
3. **位操作**：解析每个阀门的125位数据
4. **定时显示**：使用wxTimer实现800us间隔的流水显示
5. **网格控件**：使用wxGrid显示数据矩阵

### 性能优化
- 使用vector<vector<bool>>存储数据，节省内存
- 批量更新网格显示，提高刷新效率
- 限制显示行数，避免界面卡顿

## 注意事项

1. **网络配置**：确保IP地址和端口配置正确
2. **防火墙**：确保UDP端口未被防火墙阻止
3. **数据格式**：发送的数据必须符合协议规范
4. **性能**：大量阀门时可能影响显示性能
5. **时间精度**：wxTimer的最小间隔为1ms，实际显示间隔可能略大于800us

## 故障排除

### 常见问题
1. **无法连接**：检查IP地址和端口设置
2. **收不到数据**：检查发送端是否正常工作
3. **显示异常**：检查阀门数量设置是否正确
4. **性能问题**：减少阀门数量或优化显示频率

### 调试信息
程序会在日志区域显示详细的调试信息，包括：
- 连接状态
- 接收到的数据包信息
- 解析结果
- 错误信息
