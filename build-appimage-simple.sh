#!/bin/bash

# 简化版 AppImage 构建脚本
# 专注于解决依赖问题

set -e

APP_NAME="MiniTool"
APP_VERSION="1.0.2"
APP_DIR="AppDir"

echo "=== 简化版 AppImage 构建脚本 ==="
echo ""

# 清理并创建目录
rm -rf "$APP_DIR"
mkdir -p "$APP_DIR/usr/bin"
mkdir -p "$APP_DIR/usr/lib"
mkdir -p "$APP_DIR/usr/share/applications"

# 复制主程序
echo "1. 复制主程序..."
cp "build/mini_tool" "$APP_DIR/usr/bin/"
chmod +x "$APP_DIR/usr/bin/mini_tool"

if [ -f "server-info" ]; then
    cp "server-info" "$APP_DIR/usr/bin/"
    chmod +x "$APP_DIR/usr/bin/server-info"
fi

# 复制编译脚本
echo "1.1. 复制编译脚本..."
if [ -f "scripts/single_pkg_build.sh" ]; then
    cp "scripts/single_pkg_build.sh" "$APP_DIR/usr/bin/"
    chmod +x "$APP_DIR/usr/bin/single_pkg_build.sh"
    echo "  ✅ 编译脚本已打包到AppImage中"
else
    echo "  ⚠️  编译脚本不存在: scripts/single_pkg_build.sh"
fi

# 复制Python文件
echo "1.2. 复制Python文件..."
mkdir -p "$APP_DIR/usr/share/mini_tool"

# 复制主要的Python文件
if [ -f "mcp_file_server.py" ]; then
    # 复制到share目录（标准位置）
    cp "mcp_file_server.py" "$APP_DIR/usr/share/mini_tool/"
    chmod +x "$APP_DIR/usr/share/mini_tool/mcp_file_server.py"

    # 同时在bin目录创建副本（兼容性）
    cp "mcp_file_server.py" "$APP_DIR/usr/bin/"
    chmod +x "$APP_DIR/usr/bin/mcp_file_server.py"

    echo "  ✅ MCP文件服务器已打包到AppImage中（bin和share目录）"
else
    echo "  ⚠️  MCP文件服务器不存在: mcp_file_server.py"
fi

# 复制测试文件（如果存在）
if [ -f "build/test_mcp.py" ]; then
    cp "build/test_mcp.py" "$APP_DIR/usr/share/mini_tool/"
    chmod +x "$APP_DIR/usr/share/mini_tool/test_mcp.py"
    echo "  ✅ MCP测试脚本已打包到AppImage中"
fi

# 复制配置文件（如果存在）
if [ -f "mcp_allowed_paths.conf" ]; then
    cp "mcp_allowed_paths.conf" "$APP_DIR/usr/share/mini_tool/"
    echo "  ✅ MCP配置文件已打包到AppImage中"
fi

# 检查Python依赖
echo "1.3. 检查Python环境..."
if command -v python3 &> /dev/null; then
    PYTHON_VERSION=$(python3 --version 2>&1 | cut -d' ' -f2)
    echo "  ✅ 系统Python版本: $PYTHON_VERSION"

    # 检查必要的Python模块
    echo "  检查Python模块依赖:"
    for module in asyncio json sys os zipfile; do
        if python3 -c "import $module" 2>/dev/null; then
            echo "    ✅ $module"
        else
            echo "    ❌ $module (缺失)"
        fi
    done
else
    echo "  ⚠️  系统未安装Python3，AppImage可能无法正常运行Python功能"
fi

# 改进依赖分析部分
echo ""
echo "2. 分析依赖..."
echo "程序依赖的库文件:"

# 创建临时文件存储依赖信息
DEPS_FILE=$(mktemp)
ldd "build/mini_tool" | grep "=>" > "$DEPS_FILE"

# 统计依赖总数
TOTAL_DEPS=$(cat "$DEPS_FILE" | wc -l)
echo "总依赖数: $TOTAL_DEPS"

# 分类依赖库
SYSTEM_CORE_LIBS=0
GUI_LIBS=0
APP_SPECIFIC_LIBS=0

# 系统核心库路径模式
SYSTEM_PATTERNS="/lib/ld-linux|/lib/libc\.|/lib/libpthread\.|/lib/libdl\.|/lib/libm\.|/lib/librt\.|/lib64/ld-linux|/lib64/libc\.|/lib64/libpthread\.|/lib64/libdl\.|/lib64/libm\.|/lib64/librt\."

# 分析每个依赖
cat "$DEPS_FILE" | while read line; do
    lib_path=$(echo $line | awk '{print $3}')
    lib_name=$(echo $line | awk '{print $1}')
    
    # 跳过not found的库
    if [[ "$lib_path" == "not" ]]; then
        echo "  ❌ 未找到: $lib_name"
        continue
    fi
    
    # 分类库文件
    if [[ $lib_path =~ $SYSTEM_PATTERNS ]]; then
        echo "  🔒 系统核心库 (无需打包): $lib_name -> $lib_path"
        SYSTEM_CORE_LIBS=$((SYSTEM_CORE_LIBS+1))
    elif [[ $lib_path == *"libwx"* ]] || [[ $lib_path == *"libgtk"* ]] || [[ $lib_path == *"libgdk"* ]] || 
         [[ $lib_path == *"libpango"* ]] || [[ $lib_path == *"libcairo"* ]] || [[ $lib_path == *"libgobject"* ]] || 
         [[ $lib_path == *"libglib"* ]] || [[ $lib_path == *"libatk"* ]]; then
        echo "  🎨 GUI库 (需要打包): $lib_name -> $lib_path"
        GUI_LIBS=$((GUI_LIBS+1))
    elif [[ $lib_path == "/usr/lib"* ]]; then
        echo "  📦 应用特定库 (建议打包): $lib_name -> $lib_path"
        APP_SPECIFIC_LIBS=$((APP_SPECIFIC_LIBS+1))
    else
        echo "  ⚙️ 其他系统库 (可能需要打包): $lib_name -> $lib_path"
    fi
done

# 打印统计信息
echo ""
echo "依赖库统计:"
echo "  系统核心库 (无需打包): $SYSTEM_CORE_LIBS"
echo "  GUI库 (需要打包): $GUI_LIBS"
echo "  应用特定库 (建议打包): $APP_SPECIFIC_LIBS"
echo "  其他: $((TOTAL_DEPS - SYSTEM_CORE_LIBS - GUI_LIBS - APP_SPECIFIC_LIBS))"

# 清理临时文件
rm "$DEPS_FILE"

# 复制关键依赖
echo ""
echo "3. 复制关键依赖..."

# 定义需要打包的库的模式
INCLUDE_PATTERNS="libwx|libgtk|libgdk|libpango|libcairo|libgobject|libglib|libgdk_pixbuf|libatk|libX11|libwayland|libxkb|libfontconfig|libfreetype|libharfbuzz|libepoxy"

# 定义不需要打包的系统库模式
EXCLUDE_PATTERNS="/lib/ld-linux|/lib/libc\.|/lib/libpthread\.|/lib/libdl\.|/lib/libm\.|/lib/librt\.|/lib64/ld-linux|/lib64/libc\.|/lib64/libpthread\.|/lib64/libdl\.|/lib64/libm\.|/lib64/librt\."

# 获取需要打包的依赖库
DEPS=$(ldd "build/mini_tool" | grep "=> /" | awk '{print $3}' | grep -E "$INCLUDE_PATTERNS" | grep -v -E "$EXCLUDE_PATTERNS" | sort | uniq)

# 添加其他可能需要的特定库
EXTRA_DEPS=$(ldd "build/mini_tool" | grep "=> /" | awk '{print $3}' | grep "/usr/lib" | grep -v -E "$EXCLUDE_PATTERNS" | sort | uniq)
DEPS="$DEPS $EXTRA_DEPS"

echo "需要复制的依赖库:"
for dep in $DEPS; do
    if [ -f "$dep" ]; then
        echo "  ✅ $dep"
        cp "$dep" "$APP_DIR/usr/lib/"
    else
        echo "  ❌ $dep (文件不存在)"
    fi
done

# 统计打包的库
PACKED_LIBS=$(ls -1 "$APP_DIR/usr/lib/" | wc -l)
echo ""
echo "已打包 $PACKED_LIBS 个库文件"

# 创建桌面文件
echo ""
echo "4. 创建桌面文件..."
cat > "$APP_DIR/usr/share/applications/$APP_NAME.desktop" << EOF
[Desktop Entry]
Type=Application
Name=Mini Tool
Comment=系统管理工具集合
Exec=mini_tool
Icon=mini_tool
Categories=System;Utility;
Terminal=false
StartupNotify=true
EOF

# 同时在AppDir根目录创建桌面文件（AppImage标准）
cat > "$APP_DIR/$APP_NAME.desktop" << EOF
[Desktop Entry]
Type=Application
Name=Mini Tool
Comment=系统管理工具集合
Exec=mini_tool
Icon=mini_tool
Categories=System;Utility;
Terminal=false
StartupNotify=true
EOF

# 创建PNG图标
echo "5. 创建图标..."

# 方法1: 使用ImageMagick创建PNG图标
if command -v convert &> /dev/null; then
    echo "使用ImageMagick创建PNG图标..."
    # 创建一个现代化的工具图标
    convert -size 256x256 xc:transparent \
        -fill "#2196F3" -draw "roundrectangle 20,20 236,236 20,20" \
        -fill white -draw "roundrectangle 40,40 216,216 10,10" \
        -fill "#2196F3" -draw "rectangle 60,60 90,90" \
        -fill "#2196F3" -draw "rectangle 110,60 140,90" \
        -fill "#2196F3" -draw "rectangle 160,60 190,90" \
        -fill "#2196F3" -draw "rectangle 60,110 90,140" \
        -fill "#2196F3" -draw "rectangle 110,110 140,140" \
        -fill "#2196F3" -draw "rectangle 160,110 190,140" \
        -fill "#2196F3" -draw "rectangle 60,160 90,190" \
        -fill "#2196F3" -draw "rectangle 110,160 140,190" \
        -fill "#2196F3" -draw "rectangle 160,160 190,190" \
        -font Arial -pointsize 24 -fill "#2196F3" -gravity south \
        -annotate +0+20 "TOOL" \
        "$APP_DIR/mini_tool.png"

    # 创建多种尺寸的图标
    mkdir -p "$APP_DIR/usr/share/icons/hicolor/256x256/apps"
    mkdir -p "$APP_DIR/usr/share/icons/hicolor/128x128/apps"
    mkdir -p "$APP_DIR/usr/share/icons/hicolor/64x64/apps"
    mkdir -p "$APP_DIR/usr/share/icons/hicolor/48x48/apps"
    mkdir -p "$APP_DIR/usr/share/icons/hicolor/32x32/apps"
    mkdir -p "$APP_DIR/usr/share/icons/hicolor/16x16/apps"

    # 复制和缩放图标到不同尺寸
    cp "$APP_DIR/mini_tool.png" "$APP_DIR/usr/share/icons/hicolor/256x256/apps/"
    convert "$APP_DIR/mini_tool.png" -resize 128x128 "$APP_DIR/usr/share/icons/hicolor/128x128/apps/mini_tool.png"
    convert "$APP_DIR/mini_tool.png" -resize 64x64 "$APP_DIR/usr/share/icons/hicolor/64x64/apps/mini_tool.png"
    convert "$APP_DIR/mini_tool.png" -resize 48x48 "$APP_DIR/usr/share/icons/hicolor/48x48/apps/mini_tool.png"
    convert "$APP_DIR/mini_tool.png" -resize 32x32 "$APP_DIR/usr/share/icons/hicolor/32x32/apps/mini_tool.png"
    convert "$APP_DIR/mini_tool.png" -resize 16x16 "$APP_DIR/usr/share/icons/hicolor/16x16/apps/mini_tool.png"

    # AppImage特殊要求：在根目录放置图标文件（已经存在，无需复制）
    # cp "$APP_DIR/mini_tool.png" "$APP_DIR/"  # 文件已在根目录

    # 创建.DirIcon符号链接（AppImage标准）
    ln -sf mini_tool.png "$APP_DIR/.DirIcon"

    echo "✅ 创建了PNG图标（多种尺寸）"

# 方法2: 如果有现成的PNG图标文件
elif [ -f "mini_tool.png" ]; then
    echo "使用现有的PNG图标文件..."
    mkdir -p "$APP_DIR/usr/share/icons/hicolor/256x256/apps"
    cp "mini_tool.png" "$APP_DIR/mini_tool.png"
    cp "mini_tool.png" "$APP_DIR/usr/share/icons/hicolor/256x256/apps/"
    echo "✅ 使用了现有PNG图标"

# 方法3: 从XPM转换为PNG（如果有其他转换工具）
elif command -v xpmtoppm &> /dev/null && command -v ppmtopng &> /dev/null; then
    echo "使用netpbm工具转换XPM到PNG..."
    # 先创建XPM，然后转换
    cat > "$APP_DIR/mini_tool.xpm" << 'EOF'
/* XPM */
static char * mini_tool_xpm[] = {
"64 64 4 1",
" 	c None",
".	c #2196F3",
"+	c #FFFFFF",
"#	c #1976D2",
"................................................................",
"................................................................",
"..++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++..",
"..+..........................................................+..",
"..+..........................................................+..",
"..+..........................................................+..",
"..+..........................................................+..",
"..+.......######....######....######....######.............+..",
"..+.......##++++#...##++++#...##++++#...##++++#.............+..",
"..+.......##++++#...##++++#...##++++#...##++++#.............+..",
"..+.......##++++#...##++++#...##++++#...##++++#.............+..",
"..+.......######....######....######....######.............+..",
"..+..........................................................+..",
"..+..........................................................+..",
"..+.......######....######....######....######.............+..",
"..+.......##++++#...##++++#...##++++#...##++++#.............+..",
"..+.......##++++#...##++++#...##++++#...##++++#.............+..",
"..+.......##++++#...##++++#...##++++#...##++++#.............+..",
"..+.......######....######....######....######.............+..",
"..+..........................................................+..",
"..+..........................................................+..",
"..+.......######....######....######....######.............+..",
"..+.......##++++#...##++++#...##++++#...##++++#.............+..",
"..+.......##++++#...##++++#...##++++#...##++++#.............+..",
"..+.......##++++#...##++++#...##++++#...##++++#.............+..",
"..+.......######....######....######....######.............+..",
"..+..........................................................+..",
"..+..........................................................+..",
"..+.......######....######....######....######.............+..",
"..+.......##++++#...##++++#...##++++#...##++++#.............+..",
"..+.......##++++#...##++++#...##++++#...##++++#.............+..",
"..+.......##++++#...##++++#...##++++#...##++++#.............+..",
"..+.......######....######....######....######.............+..",
"..+..........................................................+..",
"..+..........................................................+..",
"..+..........................................................+..",
"..+..........................................................+..",
"..+..........................................................+..",
"..+..........................................................+..",
"..+..........................................................+..",
"..+..........................................................+..",
"..+..........................................................+..",
"..+..........................................................+..",
"..+..........................................................+..",
"..+..........................................................+..",
"..+..........................................................+..",
"..+..........................................................+..",
"..+..........................................................+..",
"..+..........................................................+..",
"..+..........................................................+..",
"..+..........................................................+..",
"..+..........................................................+..",
"..+..........................................................+..",
"..+..........................................................+..",
"..+..........................................................+..",
"..+..........................................................+..",
"..+..........................................................+..",
"..+..........................................................+..",
"..+..........................................................+..",
"..++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++..",
"................................................................",
"................................................................",
"................................................................",
"................................................................",
"................................................................",
"................................................................",
"................................................................"};
EOF

    # 转换XPM到PNG
    xpmtoppm "$APP_DIR/mini_tool.xpm" | ppmtopng > "$APP_DIR/mini_tool.png"

    mkdir -p "$APP_DIR/usr/share/icons/hicolor/256x256/apps"
    cp "$APP_DIR/mini_tool.png" "$APP_DIR/usr/share/icons/hicolor/256x256/apps/"

    echo "✅ 从XPM转换为PNG图标"
else
    # 如果没有ImageMagick，创建一个简单的XPM图标
    cat > "$APP_DIR/mini_tool.xpm" << 'EOF'
/* XPM */
static char * mini_tool_xpm[] = {
"64 64 4 1",
" 	c None",
".	c #2196F3",
"+	c #FFFFFF",
"#	c #1976D2",
"................................................................",
"................................................................",
"..++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++..",
"..+..........................................................+..",
"..+..........................................................+..",
"..+..........................................................+..",
"..+..........................................................+..",
"..+.......######....######....######....######.............+..",
"..+.......##++++#...##++++#...##++++#...##++++#.............+..",
"..+.......##++++#...##++++#...##++++#...##++++#.............+..",
"..+.......##++++#...##++++#...##++++#...##++++#.............+..",
"..+.......######....######....######....######.............+..",
"..+..........................................................+..",
"..+..........................................................+..",
"..+.......######....######....######....######.............+..",
"..+.......##++++#...##++++#...##++++#...##++++#.............+..",
"..+.......##++++#...##++++#...##++++#...##++++#.............+..",
"..+.......##++++#...##++++#...##++++#...##++++#.............+..",
"..+.......######....######....######....######.............+..",
"..+..........................................................+..",
"..+..........................................................+..",
"..+.......######....######....######....######.............+..",
"..+.......##++++#...##++++#...##++++#...##++++#.............+..",
"..+.......##++++#...##++++#...##++++#...##++++#.............+..",
"..+.......##++++#...##++++#...##++++#...##++++#.............+..",
"..+.......######....######....######....######.............+..",
"..+..........................................................+..",
"..+..........................................................+..",
"..+.......######....######....######....######.............+..",
"..+.......##++++#...##++++#...##++++#...##++++#.............+..",
"..+.......##++++#...##++++#...##++++#...##++++#.............+..",
"..+.......##++++#...##++++#...##++++#...##++++#.............+..",
"..+.......######....######....######....######.............+..",
"..+..........................................................+..",
"..+..........................................................+..",
"..+..........................................................+..",
"..+..........................................................+..",
"..+..........................................................+..",
"..+..........................................................+..",
"..+..........................................................+..",
"..+..........................................................+..",
"..+..........................................................+..",
"..+..........................................................+..",
"..+..........................................................+..",
"..+..........................................................+..",
"..+..........................................................+..",
"..+..........................................................+..",
"..+..........................................................+..",
"..+..........................................................+..",
"..+..........................................................+..",
"..+..........................................................+..",
"..+..........................................................+..",
"..+..........................................................+..",
"..+..........................................................+..",
"..+..........................................................+..",
"..+..........................................................+..",
"..+..........................................................+..",
"..+..........................................................+..",
"..++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++..",
"................................................................",
"................................................................",
"................................................................",
"................................................................",
"................................................................",
"................................................................",
"................................................................"};
EOF

    # 复制XPM图标
    mkdir -p "$APP_DIR/usr/share/pixmaps"
    cp "$APP_DIR/mini_tool.xpm" "$APP_DIR/usr/share/pixmaps/"

    echo "⚠️  创建了XPM图标"
    echo ""
    echo "💡 要获得更好的PNG图标，请安装ImageMagick："
    echo "   Ubuntu/Debian: sudo apt-get install imagemagick"
    echo "   CentOS/RHEL:   sudo yum install ImageMagick"
    echo "   Fedora:        sudo dnf install ImageMagick"
    echo "   然后重新运行此脚本"
fi

# 复制桌面文件到根目录
cp "$APP_DIR/usr/share/applications/$APP_NAME.desktop" "$APP_DIR/"

# 创建AppRun脚本
echo "6. 创建AppRun脚本..."
cat > "$APP_DIR/AppRun" << 'EOF'
#!/bin/bash

# AppRun for Mini Tool

HERE="$(dirname "$(readlink -f "${0}")")"

# 设置库路径
export LD_LIBRARY_PATH="${HERE}/usr/lib:${LD_LIBRARY_PATH}"

# 设置PATH
export PATH="${HERE}/usr/bin:${PATH}"

# 设置Python路径，使Python脚本能找到相关模块
export PYTHONPATH="${HERE}/usr/share/mini_tool:${PYTHONPATH}"

# 设置工作目录为临时目录
cd /tmp

# 运行程序
exec "${HERE}/usr/bin/mini_tool" "$@"
EOF

chmod +x "$APP_DIR/AppRun"

# 创建Python服务器启动脚本
echo "6.1. 创建Python服务器启动脚本..."
if [ -f "mcp_file_server.py" ]; then
    # 创建改进的启动脚本
    cat > "$APP_DIR/usr/bin/mcp-server" << 'EOF'
#!/bin/bash

# MCP File Server启动脚本

HERE="$(dirname "$(readlink -f "${0}")")"

# 设置Python路径
export PYTHONPATH="${HERE}/../share/mini_tool:${HERE}:${PYTHONPATH}"

# 尝试多个可能的Python文件位置
PYTHON_SCRIPT=""
if [ -f "${HERE}/mcp_file_server.py" ]; then
    PYTHON_SCRIPT="${HERE}/mcp_file_server.py"
elif [ -f "${HERE}/../share/mini_tool/mcp_file_server.py" ]; then
    PYTHON_SCRIPT="${HERE}/../share/mini_tool/mcp_file_server.py"
else
    echo "错误: 找不到mcp_file_server.py" >&2
    exit 1
fi

# 检查Python是否可用
if ! command -v python3 &> /dev/null; then
    echo "错误: 系统未安装Python3" >&2
    exit 1
fi

# 运行Python服务器
exec python3 "$PYTHON_SCRIPT" "$@"
EOF
    chmod +x "$APP_DIR/usr/bin/mcp-server"
    echo "  ✅ MCP服务器启动脚本已创建"

    # 创建直接的Python启动脚本（用于兼容性）
    # 这个脚本会被C++程序直接调用
    cat > "$APP_DIR/usr/bin/mcp_file_server.py" << 'EOF'
#!/usr/bin/env python3

# MCP File Server - AppImage兼容包装器
# 这个脚本确保在AppImage环境中能正确找到并运行MCP服务器

import sys
import os
import asyncio

# 获取脚本所在目录
script_dir = os.path.dirname(os.path.abspath(__file__))

# 定义可能的Python文件位置
possible_paths = [
    script_dir,  # /usr/bin/
    os.path.join(script_dir, '..', 'share', 'mini_tool'),  # /usr/share/mini_tool/
]

# 添加路径到sys.path
for path in possible_paths:
    if os.path.exists(path) and path not in sys.path:
        sys.path.insert(0, path)

# 查找实际的mcp_file_server.py文件
actual_script = None
for path in possible_paths:
    candidate = os.path.join(path, 'mcp_file_server.py')
    if os.path.exists(candidate) and candidate != __file__:
        actual_script = candidate
        break

if actual_script:
    # 直接执行找到的脚本文件
    try:
        with open(actual_script, 'r', encoding='utf-8') as f:
            code = f.read()

        # 设置__file__变量为实际脚本路径
        globals_dict = {
            '__file__': actual_script,
            '__name__': '__main__'
        }

        exec(code, globals_dict)
    except Exception as e:
        print(f"执行MCP服务器时出错: {e}", file=sys.stderr)
        sys.exit(1)
else:
    print("错误: 无法找到mcp_file_server.py文件", file=sys.stderr)
    print(f"搜索路径: {possible_paths}", file=sys.stderr)
    sys.exit(1)
EOF
    chmod +x "$APP_DIR/usr/bin/mcp_file_server.py"
    echo "  ✅ Python包装器脚本已创建"
fi

# 检查结果
echo ""
echo "7. 检查构建结果..."
echo "AppDir 结构:"
find "$APP_DIR" -type f | sort

echo ""
echo "复制的依赖库 ($(ls -1 "$APP_DIR/usr/lib/" | wc -l) 个):"
ls -la "$APP_DIR/usr/lib/"

echo ""
echo "打包的Python文件:"
if [ -d "$APP_DIR/usr/share/mini_tool" ]; then
    ls -la "$APP_DIR/usr/share/mini_tool/"
else
    echo "  无Python文件"
fi

echo ""
echo "可执行文件:"
ls -la "$APP_DIR/usr/bin/"

# 测试AppRun
echo ""
echo "8. 测试AppRun..."
if "$APP_DIR/AppRun" --help 2>/dev/null; then
    echo "✅ AppRun 测试成功"
else
    echo "⚠️  AppRun 测试失败，但这可能是正常的（GUI程序无法在无显示环境运行）"
fi

# 下载并使用appimagetool
echo ""
echo "9. 创建AppImage..."

if [ ! -f "appimagetool-x86_64.AppImage" ]; then
    echo "下载 appimagetool..."
    wget -q "https://github.com/AppImage/AppImageKit/releases/download/continuous/appimagetool-x86_64.AppImage"
    chmod +x appimagetool-x86_64.AppImage
fi

# 确保runtime文件存在
if [ ! -f "runtime-x86_64" ]; then
    echo "下载 runtime..."
    wget -q "https://github.com/AppImage/type2-runtime/releases/download/continuous/runtime-x86_64"
fi

# 创建AppImage，使用本地runtime文件
echo "使用本地runtime创建AppImage..."
ARCH=x86_64 ./appimagetool-x86_64.AppImage --runtime-file runtime-x86_64 --no-appstream "$APP_DIR" "$APP_NAME-$APP_VERSION-x86_64.AppImage"

if [ -f "$APP_NAME-$APP_VERSION-x86_64.AppImage" ]; then
    chmod +x "$APP_NAME-$APP_VERSION-x86_64.AppImage"
    
    echo ""
    echo "🎉 AppImage 创建成功!"
    echo "文件: $APP_NAME-$APP_VERSION-x86_64.AppImage"
    echo "大小: $(du -h "$APP_NAME-$APP_VERSION-x86_64.AppImage" | cut -f1)"
    echo ""
    echo "使用方法:"
    echo "  chmod +x $APP_NAME-$APP_VERSION-x86_64.AppImage"
    echo "  ./$APP_NAME-$APP_VERSION-x86_64.AppImage"
    echo ""
    echo "Python功能使用:"
    if [ -f "mcp_file_server.py" ]; then
        echo "  # 提取AppImage内容到临时目录"
        echo "  ./$APP_NAME-$APP_VERSION-x86_64.AppImage --appimage-extract"
        echo "  # 运行MCP文件服务器"
        echo "  ./squashfs-root/usr/bin/mcp-server --allowed-paths /path/to/allowed/dir"
        echo "  # 或者直接运行Python文件"
        echo "  python3 ./squashfs-root/usr/share/mini_tool/mcp_file_server.py"
    fi
    echo ""
    echo "优势:"
    echo "  ✅ 包含所有必要依赖"
    echo "  ✅ 包含Python脚本和模块"
    echo "  ✅ 无需安装，直接运行"
    echo "  ✅ 兼容大多数Linux发行版"
    echo "  ✅ 单文件分发"
else
    echo "❌ AppImage 创建失败"
    exit 1
fi
