#!/usr/bin/env python3
"""
测试UDP发送程序，用于测试显示喷吹指令功能
发送模拟的阀数据包到指定的IP和端口
"""

import socket
import struct
import time
import random

def create_valve_data_packet(packet_type, valve_count=90, udp_send_num=1):
    """创建阀数据包"""
    # 计算当前包的阀范围
    valve_start = (packet_type - 1) * 80 + 1
    valve_end = min(packet_type * 80, valve_count)
    actual_valve_count = valve_end - valve_start + 1
    
    # 包头数据
    packet_size = 32 + actual_valve_count * 16
    blow_delay = 500
    trigger_time = 800
    total_packets = (valve_count + 79) // 80
    
    # 转换为网络字节序（大端）
    header = struct.pack('>IIIIII8s', 
                        packet_type,      # 包类型
                        packet_size,      # 包大小
                        blow_delay,       # 喷吹延时
                        trigger_time,     # 触发时间
                        total_packets,    # 总包数
                        udp_send_num,     # UDP序号
                        b'\x00' * 8)      # 保留字节
    
    # 生成阀数据（每个阀16字节，125行数据）
    valve_data = bytearray()
    for valve_idx in range(actual_valve_count):
        valve_number = valve_start + valve_idx
        
        # 为每个阀生成16字节的数据（125位有效）
        valve_bytes = bytearray(16)
        
        # 生成测试模式：简单的开关模式
        for row in range(125):
            byte_idx = row // 8
            bit_idx = 7 - (row % 8)  # 高位在前
            
            # 创建一些测试模式
            if valve_number <= 10:
                # 前10个阀：交替模式
                bit_value = (row + valve_number) % 2
            elif valve_number <= 20:
                # 11-20阀：每5行一个周期
                bit_value = (row // 5) % 2
            else:
                # 其他阀：随机模式（但保持一定规律）
                bit_value = (row + valve_number * 3) % 3 == 0
            
            if bit_value:
                valve_bytes[byte_idx] |= (1 << bit_idx)
        
        valve_data.extend(valve_bytes)
    
    # 如果不足80个阀，用0填充
    while len(valve_data) < 80 * 16:
        valve_data.extend(b'\x00' * 16)
    
    return header + valve_data

def send_test_packets(target_ip='**************', target_port=9020, valve_count=90):
    """发送测试数据包"""
    sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
    
    try:
        total_packets = (valve_count + 79) // 80
        udp_send_num = 1
        
        print(f"开始发送测试数据包到 {target_ip}:{target_port}")
        print(f"阀门数量: {valve_count}, 总包数: {total_packets}")
        
        while True:
            print(f"\n发送第 {udp_send_num} 轮数据包...")
            
            # 发送所有包
            for packet_type in range(1, total_packets + 1):
                packet = create_valve_data_packet(packet_type, valve_count, udp_send_num)
                sock.sendto(packet, (target_ip, target_port))
                print(f"  发送第 {packet_type} 包，大小: {len(packet)} 字节")
                time.sleep(0.01)  # 10ms间隔
            
            udp_send_num += 1
            time.sleep(0.1)  # 100ms间隔，模拟实际发送频率
            
    except KeyboardInterrupt:
        print("\n停止发送")
    except Exception as e:
        print(f"发送错误: {e}")
    finally:
        sock.close()

if __name__ == "__main__":
    import sys
    
    # 默认参数
    target_ip = '**************'
    target_port = 9020
    valve_count = 90
    
    # 解析命令行参数
    if len(sys.argv) > 1:
        target_ip = sys.argv[1]
    if len(sys.argv) > 2:
        target_port = int(sys.argv[2])
    if len(sys.argv) > 3:
        valve_count = int(sys.argv[3])
    
    print("UDP测试发送程序")
    print("用法: python3 test_udp_sender.py [目标IP] [目标端口] [阀门数量]")
    print(f"当前设置: IP={target_ip}, 端口={target_port}, 阀门数量={valve_count}")
    print("按Ctrl+C停止发送")
    
    send_test_packets(target_ip, target_port, valve_count)
